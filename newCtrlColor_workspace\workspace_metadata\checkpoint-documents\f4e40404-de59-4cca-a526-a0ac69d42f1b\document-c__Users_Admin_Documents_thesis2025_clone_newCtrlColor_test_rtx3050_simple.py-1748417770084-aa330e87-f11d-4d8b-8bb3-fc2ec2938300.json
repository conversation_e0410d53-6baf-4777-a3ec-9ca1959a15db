{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\newCtrlColor\\test_rtx3050_simple.py"}, "modifiedCode": "\"\"\"\nSimple RTX 3050 Optimized Test Script\n\nThis is the original test.py with minimal RTX 3050 optimizations added.\nJust run this instead of test.py for RTX 3050 optimization.\n\nKey changes:\n- Import RTX 3050 optimizations\n- FP16 mixed precision\n- Memory management\n- Optimal batch sizes\n\"\"\"\n\n# RTX 3050 optimizations (add this line to original)\nfrom rtx3050_config import *\n\n# Original imports (unchanged)\nimport os\nfrom share import *\nimport config\n\nimport cv2\nimport einops\nimport gradio as gr\nimport numpy as np\nimport torch\nimport random\n\nfrom pytorch_lightning import seed_everything\nfrom annotator.util import resize_image\nfrom cldm.model import create_model, load_state_dict\nfrom cldm.ddim_haced_sag_step import DDIMSampler\nfrom lavis.models import load_model_and_preprocess\nfrom PIL import Image\nimport tqdm\n\nfrom ldm.models.autoencoder_train import AutoencoderKL\n\n# Original model loading (with RTX 3050 optimizations)\nckpt_path = \"./pretrained_models/main_model.ckpt\"\n\nprint(\"🎯 Loading model with RTX 3050 optimizations...\")\nmodel = create_model('./models/cldm_v15_inpainting_infer1.yaml').cpu()\nmodel.load_state_dict(load_state_dict(ckpt_path, location='cuda'), strict=False)\nmodel = model.cuda()\n\n# Enable FP16 for RTX 3050\nif torch.cuda.is_available():\n    model = model.half()\n    print(\"✅ Model converted to FP16\")\n\nddim_sampler = DDIMSampler(model)\n\ndevice = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\nBLIP_model, vis_processors, _ = load_model_and_preprocess(name=\"blip_caption\", model_type=\"base_coco\", is_eval=True, device=device)\n\nvae_model_ckpt_path = \"./pretrained_models/content-guided_deformable_vae.ckpt\"\n\ndef load_vae():\n    init_config = {\n        \"embed_dim\": 4,\n        \"monitor\": \"val/rec_loss\",\n        \"ddconfig\": {\n            \"double_z\": True,\n            \"z_channels\": 4,\n            \"resolution\": 256,\n            \"in_channels\": 3,\n            \"out_ch\": 3,\n            \"ch\": 128,\n            \"ch_mult\": [1, 2, 4, 4],\n            \"num_res_blocks\": 2,\n            \"attn_resolutions\": [],\n            \"dropout\": 0.0,\n        },\n        \"lossconfig\": {\n            \"target\": \"ldm.modules.losses.LPIPSWithDiscriminator\",\n            \"params\": {\n                \"disc_start\": 501,\n                \"kl_weight\": 0,\n                \"disc_weight\": 0.025,\n                \"disc_factor\": 1.0\n            }\n        }\n    }\n    vae = AutoencoderKL(**init_config)\n    vae.load_state_dict(load_state_dict(vae_model_ckpt_path, location='cuda'))\n    vae = vae.cuda()\n\n    # Enable FP16 for VAE too\n    if torch.cuda.is_available():\n        vae = vae.half()\n\n    return vae\n\nvae_model = load_vae()\n\n# All original functions preserved (encode_mask, get_mask, etc.)\ndef encode_mask(mask, masked_image):\n    mask = torch.nn.functional.interpolate(mask, size=(mask.shape[2] // 8, mask.shape[3] // 8))\n    mask = mask.to(device=\"cuda\")\n\n    # Use FP16 autocast for RTX 3050\n    with torch.cuda.amp.autocast(enabled=True):\n        masked_image_latents = model.get_first_stage_encoding(model.encode_first_stage(masked_image.cuda())).detach()\n\n    return mask, masked_image_latents\n\ndef get_mask(input_image, hint_image):\n    mask = input_image.copy()\n    H, W, C = input_image.shape\n    for i in range(H):\n        for j in range(W):\n            if input_image[i, j, 0] == hint_image[i, j, 0]:\n                mask[i, j, :] = 255.\n            else:\n                mask[i, j, :] = 0.\n    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))\n    mask = cv2.morphologyEx(np.array(mask), cv2.MORPH_OPEN, kernel, iterations=1)\n    return mask\n\ndef prepare_mask_and_masked_image(image, mask):\n    # [Original function preserved - too long to include here]\n    # This function remains exactly the same as original\n    if isinstance(image, torch.Tensor):\n        if not isinstance(mask, torch.Tensor):\n            raise TypeError(f\"`image` is a torch.Tensor but `mask` (type: {type(mask)} is not\")\n        if image.ndim == 3:\n            assert image.shape[0] == 3, \"Image outside a batch should be of shape (3, H, W)\"\n            image = image.unsqueeze(0)\n        if mask.ndim == 2:\n            mask = mask.unsqueeze(0).unsqueeze(0)\n        if mask.ndim == 3:\n            if mask.shape[0] == 1:\n                mask = mask.unsqueeze(0)\n            else:\n                mask = mask.unsqueeze(1)\n        assert image.ndim == 4 and mask.ndim == 4, \"Image and Mask must have 4 dimensions\"\n        assert image.shape[-2:] == mask.shape[-2:], \"Image and Mask must have the same spatial dimensions\"\n        assert image.shape[0] == mask.shape[0], \"Image and Mask must have the same batch size\"\n        if image.min() < -1 or image.max() > 1:\n            raise ValueError(\"Image should be in [-1, 1] range\")\n        if mask.min() < 0 or mask.max() > 1:\n            raise ValueError(\"Mask should be in [0, 1] range\")\n        mask[mask < 0.5] = 0\n        mask[mask >= 0.5] = 1\n        image = image.to(dtype=torch.float32)\n    elif isinstance(mask, torch.Tensor):\n        raise TypeError(f\"`mask` is a torch.Tensor but `image` (type: {type(image)} is not\")\n    else:\n        if isinstance(image, (Image.Image, np.ndarray)):\n            image = [image]\n        if isinstance(image, list) and isinstance(image[0], Image.Image):\n            image = [np.array(i.convert(\"RGB\"))[None, :] for i in image]\n            image = np.concatenate(image, axis=0)\n        elif isinstance(image, list) and isinstance(image[0], np.ndarray):\n            image = np.concatenate([i[None, :] for i in image], axis=0)\n        image = image.transpose(0, 3, 1, 2)\n        image = torch.from_numpy(image).to(dtype=torch.float32) / 127.5 - 1.0\n        if isinstance(mask, (Image.Image, np.ndarray)):\n            mask = [mask]\n        if isinstance(mask, list) and isinstance(mask[0], Image.Image):\n            mask = np.concatenate([np.array(m.convert(\"L\"))[None, None, :] for m in mask], axis=0)\n            mask = mask.astype(np.float32) / 255.0\n        elif isinstance(mask, list) and isinstance(mask[0], np.ndarray):\n            mask = np.concatenate([m[None, None, :] for m in mask], axis=0)\n        mask[mask < 0.5] = 0\n        mask[mask >= 0.5] = 1\n        mask = torch.from_numpy(mask)\n    masked_image = image * (mask < 0.5)\n    return mask, masked_image\n\ndef is_gray_scale(img, threshold=10):\n    img = Image.fromarray(img)\n    if len(img.getbands()) == 1:\n        return True\n    img1 = np.asarray(img.getchannel(channel=0), dtype=np.int16)\n    img2 = np.asarray(img.getchannel(channel=1), dtype=np.int16)\n    img3 = np.asarray(img.getchannel(channel=2), dtype=np.int16)\n    diff1 = (img1 - img2).var()\n    diff2 = (img2 - img3).var()\n    diff3 = (img3 - img1).var()\n    diff_sum = (diff1 + diff2 + diff3) / 3.0\n    if diff_sum <= threshold:\n        return True\n    else:\n        return False\n\n# Original process function with RTX 3050 optimizations\ndef process(using_deformable_vae, change_according_to_strokes, iterative_editing, input_image, hint_image, prompt, a_prompt, n_prompt, num_samples, image_resolution, ddim_steps, guess_mode, strength, scale, sag_scale, SAG_influence_step, seed, eta):\n\n    # RTX 3050 optimization: Clear cache and limit batch size\n    clear_memory()\n    num_samples = min(num_samples, 1)  # Limit to 1 for RTX 3050\n    image_resolution = min(image_resolution, 512)  # Conservative resolution\n\n    print(f\"🎯 RTX 3050 optimized settings: samples={num_samples}, resolution={image_resolution}\")\n\n    torch.cuda.empty_cache()\n    with torch.no_grad():\n        # Use FP16 autocast for entire process\n        with torch.cuda.amp.autocast(enabled=True):\n            ref_flag = True\n            input_image_ori = input_image\n            if is_gray_scale(input_image):\n                print(\"It is a greyscale image.\")\n            else:\n                print(\"It is a color image.\")\n                input_image_ori = input_image\n                input_image = cv2.cvtColor(input_image, cv2.COLOR_RGB2LAB)[:, :, 0]\n                input_image = cv2.merge([input_image, input_image, input_image])\n\n            mask = get_mask(input_image_ori, hint_image)\n            cv2.imwrite(\"gradio_mask1.png\", mask)\n\n            if iterative_editing:\n                mask = 255 - mask\n                if change_according_to_strokes:\n                    kernel = np.ones((15, 15), np.uint8)\n                    mask = cv2.morphologyEx(mask, cv2.MORPH_GRADIENT, kernel)\n                    hint_image = mask/255.*hint_image+(1-mask/255.)*hint_image\n                else:\n                    hint_image = mask/255.*input_image+(1-mask/255.)*input_image_ori\n            else:\n                hint_image = mask/255.*input_image+(1-mask/255.)*hint_image\n\n            hint_image = hint_image.astype(np.uint8)\n\n            if len(prompt) == 0:\n                image = Image.fromarray(input_image)\n                image = vis_processors[\"eval\"](image).unsqueeze(0).to(device)\n                prompt = BLIP_model.generate({\"image\": image})[0]\n                if \"a black and white photo of\" in prompt or \"black and white photograph of\" in prompt:\n                    prompt = prompt.replace(prompt[:prompt.find(\"of\")+3], \"\")\n\n            print(prompt)\n            H_ori, W_ori, C_ori = input_image.shape\n            img = resize_image(input_image, image_resolution)\n            mask = resize_image(mask, image_resolution)\n            hint_image = resize_image(hint_image, image_resolution)\n            mask, masked_image = prepare_mask_and_masked_image(Image.fromarray(hint_image), Image.fromarray(mask))\n            mask, masked_image_latents = encode_mask(mask, masked_image)\n            H, W, C = img.shape\n\n            ref_image = np.array([[[0]*C]*W]*H).astype(np.float32)\n            ref_image = resize_image(ref_image, image_resolution)\n\n            control = torch.from_numpy(img.copy()).float().cuda() / 255.0\n            control = torch.stack([control for _ in range(num_samples)], dim=0)\n            control = einops.rearrange(control, 'b h w c -> b c h w').clone()\n\n            if seed == -1:\n                seed = random.randint(0, 65535)\n            seed_everything(seed)\n\n            ref_image = cv2.resize(ref_image, (W, H))\n            ref_image = torch.from_numpy(ref_image).cuda().unsqueeze(0)\n            init_latents = None\n\n            if config.save_memory:\n                model.low_vram_shift(is_diffusing=False)\n\n            print(\"no reference images, using Frozen encoder\")\n            cond = {\"c_concat\": [control], \"c_crossattn\": [model.get_learned_conditioning([prompt + ', ' + a_prompt] * num_samples)]}\n            un_cond = {\"c_concat\": None if guess_mode else [control], \"c_crossattn\": [model.get_learned_conditioning([n_prompt] * num_samples)]}\n            shape = (4, H // 8, W // 8)\n\n            if config.save_memory:\n                model.low_vram_shift(is_diffusing=True)\n\n            # RTX 3050: Use FP16 for noise generation\n            generator = torch.manual_seed(seed)\n            noise = torch.randn(shape, generator=generator, device=device, dtype=torch.float16)\n\n            model.control_scales = [strength * (0.825 ** float(12 - i)) for i in range(13)] if guess_mode else ([strength] * 13)\n\n            samples, intermediates = ddim_sampler.sample(model, ddim_steps, num_samples,\n                                                        shape, cond, mask=mask, masked_image_latents=masked_image_latents, verbose=False, eta=eta,\n                                                        x_T=init_latents,\n                                                        unconditional_guidance_scale=scale,\n                                                        sag_scale=sag_scale,\n                                                        SAG_influence_step=SAG_influence_step,\n                                                        noise=noise,\n                                                        unconditional_conditioning=un_cond)\n\n            if config.save_memory:\n                model.low_vram_shift(is_diffusing=False)\n\n            if not using_deformable_vae:\n                x_samples = model.decode_first_stage(samples)\n            else:\n                samples = model.decode_first_stage_before_vae(samples)\n                gray_content_z = vae_model.get_gray_content_z(torch.from_numpy(img.copy()).float().cuda() / 255.0)\n                x_samples = vae_model.decode(samples, gray_content_z)\n\n            x_samples = (einops.rearrange(x_samples, 'b c h w -> b h w c') * 127.5 + 127.5).cpu().numpy().clip(0, 255).astype(np.uint8)\n\n            results_ori = [x_samples[i] for i in range(num_samples)]\n            results_ori = [cv2.resize(i, (W_ori, H_ori), interpolation=cv2.INTER_LANCZOS4) for i in results_ori]\n\n            cv2.imwrite(\"result_ori.png\", cv2.cvtColor(results_ori[0], cv2.COLOR_RGB2BGR))\n\n            results_tmp = [cv2.cvtColor(np.array(i), cv2.COLOR_RGB2LAB) for i in results_ori]\n            results = [cv2.merge([input_image[:, :, 0], tmp[:, :, 1], tmp[:, :, 2]]) for tmp in results_tmp]\n            results_mergeL = [cv2.cvtColor(np.asarray(i), cv2.COLOR_LAB2RGB) for i in results]\n            cv2.imwrite(\"output.png\", cv2.cvtColor(results_mergeL[0], cv2.COLOR_RGB2BGR))\n\n    # Clear memory after processing\n    clear_memory()\n    return results_mergeL\n\ndef get_grayscale_img(img, progress=gr.Progress(track_tqdm=True)):\n    clear_memory()  # RTX 3050 optimization\n    for j in tqdm.tqdm(range(1), desc=\"Uploading input...\"):\n        return img, \"Uploading input image done.\"\n\n# Original Gradio interface with RTX 3050 optimizations\nblock = gr.Blocks().queue()\nwith block:\n    with gr.Row():\n        gr.Markdown(\"## CtrlColor - RTX 3050 Optimized\")\n        gr.Markdown(\"**Optimizations:** FP16 enabled, Memory managed, Batch size limited\")\n\n    with gr.Row():\n        with gr.Column():\n            grayscale_img = gr.Image(visible=False, type=\"numpy\")\n            input_image = gr.Image(source='upload', tool='color-sketch', interactive=True)\n            Grayscale_button = gr.Button(value=\"Upload input image\")\n            text_out = gr.Textbox(value=\"RTX 3050 optimized! Upload image and draw strokes or input text prompts.\")\n            prompt = gr.Textbox(label=\"Prompt\")\n            change_according_to_strokes = gr.Checkbox(label='Change according to strokes\\' color', value=True)\n            iterative_editing = gr.Checkbox(label='Only change the strokes\\' area', value=False)\n            using_deformable_vae = gr.Checkbox(label='Using deformable vae (Less color overflow)', value=False)\n\n            with gr.Accordion(\"RTX 3050 Settings\", open=True):\n                num_samples = gr.Slider(1, 1, value=1, step=1, label=\"Number of samples (RTX 3050: max 1)\")\n                image_resolution = gr.Slider(256, 512, value=512, step=64, label=\"Image Resolution (RTX 3050: max 512)\")\n                ddim_steps = gr.Slider(1, 50, value=20, step=1, label=\"DDIM Steps\")\n                strength = gr.Slider(0.0, 2.0, value=1.0, step=0.01, label=\"Control Strength\")\n                guess_mode = gr.Checkbox(label='Guess Mode', value=False)\n                scale = gr.Slider(0.1, 30.0, value=9.0, step=0.1, label=\"Guidance Scale\")\n                sag_scale = gr.Slider(0.0, 1.0, value=0.05, step=0.01, label=\"SAG Scale\")\n                SAG_influence_step = gr.Slider(0, 1000, value=600, step=1, label=\"SAG Influence Step\")\n                seed = gr.Slider(-1, 2147483647, step=1, randomize=True, label=\"Seed\")\n                eta = gr.Number(label=\"eta (DDIM)\", value=0.0)\n\n            a_prompt = gr.Textbox(label=\"Added Prompt\", value='best quality, extremely detailed')\n            n_prompt = gr.Textbox(label=\"Negative Prompt\", value='longbody, lowres, bad anatomy, bad hands, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality')\n            run_button = gr.Button(label=\"Run (RTX 3050 Optimized)\", variant=\"primary\")\n\n        with gr.Column():\n            result_gallery = gr.Gallery(label='Output', show_label=False, elem_id=\"gallery\").style(grid=2, height='auto')\n\n    Grayscale_button.click(fn=get_grayscale_img, inputs=[input_image], outputs=[grayscale_img, text_out])\n    run_button.click(fn=process, inputs=[using_deformable_vae, change_according_to_strokes, iterative_editing, input_image, input_image, prompt, a_prompt, n_prompt, num_samples, image_resolution, ddim_steps, guess_mode, strength, scale, sag_scale, SAG_influence_step, seed, eta], outputs=[result_gallery])\n\nif __name__ == \"__main__\":\n    print(\"🚀 Launching RTX 3050 optimized CtrlColor...\")\n    block.launch(server_port=7860, share=False, inbrowser=True)\n"}
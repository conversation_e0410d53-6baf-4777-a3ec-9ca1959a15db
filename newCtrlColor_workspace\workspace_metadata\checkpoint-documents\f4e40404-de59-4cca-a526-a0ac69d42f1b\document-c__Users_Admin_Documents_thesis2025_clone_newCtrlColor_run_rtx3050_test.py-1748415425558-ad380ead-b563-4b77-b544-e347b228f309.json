{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/newCtrlColor/run_rtx3050_test.py"}, "modifiedCode": "\"\"\"\nRTX 3050 Test Launcher\n\nSimple launcher script that sets up the Python path correctly and runs the RTX 3050 optimization tests.\nUse this script to test your RTX 3050 setup without import issues.\n\"\"\"\n\nimport sys\nimport os\n\n# Add the project root to Python path\nproject_root = os.path.dirname(os.path.abspath(__file__))\nsys.path.insert(0, project_root)\n\ndef run_simple_test():\n    \"\"\"Run the simple RTX 3050 test\"\"\"\n    print(\"🚀 Running Simple RTX 3050 Test...\")\n    try:\n        from full.device_optimization.simple_rtx3050_test import main\n        main()\n    except ImportError as e:\n        print(f\"❌ Import error: {e}\")\n        print(\"⚠️ Running basic CUDA test instead...\")\n        run_basic_cuda_test()\n\ndef run_basic_cuda_test():\n    \"\"\"Run basic CUDA availability test\"\"\"\n    import torch\n    \n    print(\"\\n🔍 BASIC CUDA TEST\")\n    print(\"=\"*30)\n    \n    if torch.cuda.is_available():\n        print(\"✅ CUDA is available\")\n        print(f\"✅ Device count: {torch.cuda.device_count()}\")\n        \n        for i in range(torch.cuda.device_count()):\n            props = torch.cuda.get_device_properties(i)\n            print(f\"✅ GPU {i}: {props.name}\")\n            print(f\"   - Memory: {props.total_memory / 1e9:.1f}GB\")\n            print(f\"   - Compute Capability: {props.major}.{props.minor}\")\n        \n        # Test basic operations\n        try:\n            device = torch.device('cuda:0')\n            test_tensor = torch.randn(1000, 1000, device=device)\n            result = torch.matmul(test_tensor, test_tensor)\n            print(\"✅ Basic CUDA operations work\")\n            \n            # Test FP16\n            try:\n                fp16_tensor = torch.randn(512, 512, dtype=torch.float16, device=device)\n                fp16_result = torch.matmul(fp16_tensor, fp16_tensor)\n                print(\"✅ FP16 operations work\")\n            except Exception as e:\n                print(f\"❌ FP16 test failed: {e}\")\n            \n            # Test memory management\n            memory_before = torch.cuda.memory_allocated(device)\n            large_tensor = torch.randn(2000, 2000, device=device)\n            memory_after = torch.cuda.memory_allocated(device)\n            del large_tensor\n            torch.cuda.empty_cache()\n            memory_final = torch.cuda.memory_allocated(device)\n            \n            print(f\"✅ Memory test: {memory_before/1e6:.1f}MB → {memory_after/1e6:.1f}MB → {memory_final/1e6:.1f}MB\")\n            \n        except Exception as e:\n            print(f\"❌ CUDA operations failed: {e}\")\n    else:\n        print(\"❌ CUDA is not available\")\n        print(\"   - Check NVIDIA drivers\")\n        print(\"   - Check PyTorch CUDA installation\")\n\ndef run_full_test():\n    \"\"\"Run the full RTX 3050 optimization test\"\"\"\n    print(\"🚀 Running Full RTX 3050 Optimization Test...\")\n    try:\n        from full.device_optimization.test_rtx3050_optimizations import main\n        main()\n    except ImportError as e:\n        print(f\"❌ Import error: {e}\")\n        print(\"⚠️ Some dependencies missing, running simple test instead...\")\n        run_simple_test()\n\ndef run_implementation_test():\n    \"\"\"Run the complete implementation test\"\"\"\n    print(\"🚀 Running Complete Implementation Test...\")\n    try:\n        from full.test_implementation import run_all_tests\n        run_all_tests()\n    except ImportError as e:\n        print(f\"❌ Import error: {e}\")\n        print(\"⚠️ Some modules missing, running basic test instead...\")\n        run_basic_cuda_test()\n\ndef main():\n    \"\"\"Main launcher function\"\"\"\n    print(\"🎯 RTX 3050 CTRLCOLOR TEST LAUNCHER\")\n    print(\"=\"*50)\n    \n    print(\"\\nAvailable tests:\")\n    print(\"1. Basic CUDA Test (always works)\")\n    print(\"2. Simple RTX 3050 Test (minimal dependencies)\")\n    print(\"3. Full RTX 3050 Optimization Test\")\n    print(\"4. Complete Implementation Test\")\n    print(\"5. Auto-select best test\")\n    \n    try:\n        choice = input(\"\\nSelect test (1-5, default=5): \").strip()\n        if not choice:\n            choice = \"5\"\n    except KeyboardInterrupt:\n        print(\"\\n\\n👋 Test cancelled by user\")\n        return\n    \n    print(f\"\\nRunning test option {choice}...\")\n    print(\"=\"*50)\n    \n    if choice == \"1\":\n        run_basic_cuda_test()\n    elif choice == \"2\":\n        run_simple_test()\n    elif choice == \"3\":\n        run_full_test()\n    elif choice == \"4\":\n        run_implementation_test()\n    elif choice == \"5\":\n        # Auto-select best available test\n        print(\"🤖 Auto-selecting best available test...\")\n        \n        # Try tests in order of complexity\n        try:\n            from full.test_implementation import run_all_tests\n            print(\"✅ Running complete implementation test...\")\n            run_all_tests()\n        except ImportError:\n            try:\n                from full.device_optimization.test_rtx3050_optimizations import main\n                print(\"✅ Running full RTX 3050 test...\")\n                main()\n            except ImportError:\n                try:\n                    from full.device_optimization.simple_rtx3050_test import main\n                    print(\"✅ Running simple RTX 3050 test...\")\n                    main()\n                except ImportError:\n                    print(\"✅ Running basic CUDA test...\")\n                    run_basic_cuda_test()\n    else:\n        print(f\"❌ Invalid choice: {choice}\")\n        print(\"Running basic CUDA test instead...\")\n        run_basic_cuda_test()\n    \n    print(\"\\n\" + \"=\"*50)\n    print(\"🎉 Test completed!\")\n    print(\"\\n📋 Next steps:\")\n    print(\"1. If CUDA tests passed, your RTX 3050 is ready\")\n    print(\"2. Use FP16 precision for best performance\")\n    print(\"3. Keep batch sizes small (1-2)\")\n    print(\"4. Monitor memory usage during inference\")\n\nif __name__ == \"__main__\":\n    main()\n"}
{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/newCtrlColor/test_rtx3050_memory_optimized.py"}, "modifiedCode": "\"\"\"\nRTX 3050 Memory-Optimized CtrlColor Test\n\nAddresses the CUDA OOM issue from test_rtx3050_simple.log by implementing:\n- CPU-first model loading with gradual GPU transfer\n- Memory-efficient checkpoint loading\n- Dynamic memory management\n- Fallback strategies for low VRAM\n\nBased on the error analysis from command_logs/test_rtx3050_simple.log\n\"\"\"\n\nimport torch\nimport torch.nn.functional as F\nimport gc\nimport os\nimport sys\nfrom contextlib import contextmanager\n\n# Add paths\nsys.path.append('.')\nsys.path.append('./cldm')\n\nfrom cldm.model import create_model, load_state_dict\nfrom cldm.ddim_hacked_sag import DDIMSampler\n\n\nclass RTX3050MemoryManager:\n    \"\"\"Memory management for RTX 3050 with 4GB VRAM\"\"\"\n    \n    def __init__(self):\n        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n        self.max_memory_gb = 3.4  # 85% of 4GB\n        self.current_memory_gb = 0\n        \n    def get_memory_info(self):\n        \"\"\"Get current GPU memory usage\"\"\"\n        if torch.cuda.is_available():\n            allocated = torch.cuda.memory_allocated() / 1024**3\n            reserved = torch.cuda.memory_reserved() / 1024**3\n            return allocated, reserved\n        return 0, 0\n    \n    def clear_cache(self):\n        \"\"\"Clear GPU cache and run garbage collection\"\"\"\n        if torch.cuda.is_available():\n            torch.cuda.empty_cache()\n        gc.collect()\n    \n    @contextmanager\n    def memory_efficient_loading(self):\n        \"\"\"Context manager for memory-efficient operations\"\"\"\n        self.clear_cache()\n        try:\n            yield\n        finally:\n            self.clear_cache()\n    \n    def check_memory_available(self, required_gb):\n        \"\"\"Check if enough memory is available\"\"\"\n        allocated, _ = self.get_memory_info()\n        available = self.max_memory_gb - allocated\n        return available >= required_gb\n\n\ndef load_model_cpu_first(config_path, ckpt_path, memory_manager):\n    \"\"\"\n    Load model on CPU first, then transfer to GPU gradually\n    \n    This addresses the CUDA OOM error from the log by:\n    1. Loading checkpoint to CPU memory first\n    2. Creating model on CPU\n    3. Loading state dict on CPU\n    4. Transferring to GPU in chunks if possible\n    \"\"\"\n    print(\"🔧 Loading model with CPU-first strategy...\")\n    \n    with memory_manager.memory_efficient_loading():\n        # Step 1: Create model on CPU\n        print(\"   - Creating model on CPU...\")\n        model = create_model(config_path).cpu()\n        \n        # Step 2: Load checkpoint to CPU\n        print(\"   - Loading checkpoint to CPU...\")\n        try:\n            # Load checkpoint with CPU mapping to avoid GPU allocation\n            checkpoint = torch.load(ckpt_path, map_location='cpu')\n            state_dict = checkpoint if isinstance(checkpoint, dict) else checkpoint.state_dict()\n            \n            # Clean up checkpoint reference\n            del checkpoint\n            gc.collect()\n            \n        except Exception as e:\n            print(f\"   ❌ Failed to load checkpoint: {e}\")\n            return None\n        \n        # Step 3: Load state dict on CPU\n        print(\"   - Loading state dict on CPU...\")\n        try:\n            model.load_state_dict(state_dict, strict=False)\n            del state_dict\n            gc.collect()\n            \n        except Exception as e:\n            print(f\"   ❌ Failed to load state dict: {e}\")\n            return None\n        \n        # Step 4: Try to move to GPU\n        print(\"   - Attempting GPU transfer...\")\n        try:\n            # Check available memory\n            allocated, reserved = memory_manager.get_memory_info()\n            print(f\"   - GPU memory before transfer: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved\")\n            \n            # Estimate model size (rough calculation)\n            param_count = sum(p.numel() for p in model.parameters())\n            model_size_gb = param_count * 4 / 1024**3  # 4 bytes per float32 parameter\n            print(f\"   - Estimated model size: {model_size_gb:.2f}GB\")\n            \n            if memory_manager.check_memory_available(model_size_gb):\n                print(\"   - Sufficient memory available, transferring to GPU...\")\n                model = model.cuda()\n                \n                # Enable memory optimizations\n                model.half()  # Use FP16\n                print(\"   - FP16 conversion applied\")\n                \n            else:\n                print(\"   ⚠️ Insufficient GPU memory, keeping model on CPU\")\n                print(\"   - Will use CPU inference (slower but functional)\")\n                \n        except RuntimeError as e:\n            if \"out of memory\" in str(e):\n                print(\"   ⚠️ GPU OOM during transfer, falling back to CPU\")\n                model = model.cpu()\n            else:\n                raise e\n    \n    allocated, reserved = memory_manager.get_memory_info()\n    print(f\"   ✅ Model loaded. GPU memory: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved\")\n    \n    return model\n\n\ndef create_optimized_sampler(model, memory_manager):\n    \"\"\"Create memory-optimized DDIM sampler\"\"\"\n    print(\"🔧 Creating optimized sampler...\")\n    \n    try:\n        # Use fewer steps for memory efficiency\n        sampler = DDIMSampler(model)\n        \n        # Configure for low memory\n        sampler.model.model.diffusion_model.use_checkpoint = True  # Gradient checkpointing\n        \n        print(\"   ✅ Sampler created with memory optimizations\")\n        return sampler\n        \n    except Exception as e:\n        print(f\"   ❌ Failed to create sampler: {e}\")\n        return None\n\n\ndef test_inference_memory_safe(model, sampler, memory_manager):\n    \"\"\"Test inference with memory safety\"\"\"\n    print(\"🧪 Testing memory-safe inference...\")\n    \n    try:\n        with memory_manager.memory_efficient_loading():\n            # Create minimal test inputs\n            batch_size = 1\n            height, width = 256, 256  # Smaller size for memory efficiency\n            \n            # Create dummy inputs\n            device = next(model.parameters()).device\n            \n            if device.type == 'cuda':\n                # GPU inference\n                print(\"   - Running GPU inference...\")\n                hint = torch.randn(batch_size, 5, height, width, device=device, dtype=torch.float16)\n                prompt = [\"a simple test image\"]\n                \n            else:\n                # CPU inference\n                print(\"   - Running CPU inference...\")\n                hint = torch.randn(batch_size, 5, height, width, device=device, dtype=torch.float32)\n                prompt = [\"a simple test image\"]\n            \n            # Monitor memory before inference\n            allocated_before, _ = memory_manager.get_memory_info()\n            print(f\"   - Memory before inference: {allocated_before:.2f}GB\")\n            \n            # Run minimal inference test\n            with torch.no_grad():\n                # Simplified inference without full pipeline\n                timesteps = torch.randint(0, 1000, (batch_size,), device=device)\n                \n                # Test model forward pass\n                if hasattr(model, 'apply_model'):\n                    # Use ControlLDM interface\n                    noise = torch.randn(batch_size, 4, height//8, width//8, device=device)\n                    if device.type == 'cuda':\n                        noise = noise.half()\n                    \n                    # Minimal conditioning\n                    cond = {\"c_concat\": [hint], \"c_crossattn\": [prompt]}\n                    \n                    # Forward pass\n                    output = model.apply_model(noise, timesteps, cond)\n                    print(f\"   - Forward pass successful. Output shape: {output.shape}\")\n                    \n                else:\n                    print(\"   - Model doesn't have apply_model method, testing basic forward\")\n                    output = model(hint, timesteps)\n                    print(f\"   - Basic forward successful. Output shape: {output.shape}\")\n            \n            # Monitor memory after inference\n            allocated_after, _ = memory_manager.get_memory_info()\n            print(f\"   - Memory after inference: {allocated_after:.2f}GB\")\n            print(f\"   - Memory increase: {allocated_after - allocated_before:.2f}GB\")\n            \n            print(\"   ✅ Inference test successful!\")\n            return True\n            \n    except RuntimeError as e:\n        if \"out of memory\" in str(e):\n            print(f\"   ❌ CUDA OOM during inference: {e}\")\n            print(\"   💡 Try reducing image size or using CPU inference\")\n        else:\n            print(f\"   ❌ Inference error: {e}\")\n        return False\n    \n    except Exception as e:\n        print(f\"   ❌ Unexpected error during inference: {e}\")\n        return False\n\n\ndef main():\n    \"\"\"Main RTX 3050 optimized test\"\"\"\n    print(\"🚀 RTX 3050 Memory-Optimized CtrlColor Test\")\n    print(\"=\" * 60)\n    \n    # Initialize memory manager\n    memory_manager = RTX3050MemoryManager()\n    \n    # Check initial GPU state\n    if torch.cuda.is_available():\n        gpu_name = torch.cuda.get_device_name(0)\n        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3\n        print(f\"🎯 GPU: {gpu_name}\")\n        print(f\"🎯 Total VRAM: {total_memory:.1f}GB\")\n        \n        allocated, reserved = memory_manager.get_memory_info()\n        print(f\"🎯 Initial memory: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved\")\n    else:\n        print(\"⚠️ CUDA not available, will use CPU\")\n    \n    print()\n    \n    # Configuration\n    config_path = './models/cldm_v15_inpainting_infer1.yaml'\n    ckpt_path = './pretrained_models/main_model.ckpt'\n    \n    # Check if files exist\n    if not os.path.exists(config_path):\n        print(f\"❌ Config file not found: {config_path}\")\n        return False\n    \n    if not os.path.exists(ckpt_path):\n        print(f\"❌ Checkpoint file not found: {ckpt_path}\")\n        return False\n    \n    # Load model with memory optimization\n    model = load_model_cpu_first(config_path, ckpt_path, memory_manager)\n    if model is None:\n        print(\"❌ Failed to load model\")\n        return False\n    \n    # Create sampler\n    sampler = create_optimized_sampler(model, memory_manager)\n    if sampler is None:\n        print(\"❌ Failed to create sampler\")\n        return False\n    \n    # Test inference\n    success = test_inference_memory_safe(model, sampler, memory_manager)\n    \n    # Final memory report\n    print(\"\\n\" + \"=\" * 60)\n    if torch.cuda.is_available():\n        allocated, reserved = memory_manager.get_memory_info()\n        print(f\"🎯 Final GPU memory: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved\")\n        print(f\"🎯 Memory efficiency: {(allocated/memory_manager.max_memory_gb)*100:.1f}% of limit used\")\n    \n    if success:\n        print(\"✅ RTX 3050 optimization test PASSED!\")\n        print(\"💡 Model can run on RTX 3050 with memory optimizations\")\n    else:\n        print(\"❌ RTX 3050 optimization test FAILED!\")\n        print(\"💡 Consider using CPU inference or smaller models\")\n    \n    return success\n\n\nif __name__ == \"__main__\":\n    success = main()\n    sys.exit(0 if success else 1)\n"}
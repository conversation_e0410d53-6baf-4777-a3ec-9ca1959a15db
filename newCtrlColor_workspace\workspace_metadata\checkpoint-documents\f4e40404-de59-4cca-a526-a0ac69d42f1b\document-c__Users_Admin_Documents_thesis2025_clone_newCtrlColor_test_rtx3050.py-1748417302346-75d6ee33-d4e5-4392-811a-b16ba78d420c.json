{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/newCtrlColor/test_rtx3050.py"}, "originalCode": "\"\"\"\nRTX 3050 Optimized Test Script for CtrlColor\n\nThis is a modified copy of test.py with RTX 3050 specific optimizations:\n- FP16 mixed precision\n- Memory management\n- Optimal batch sizes\n- GPU memory monitoring\n- Automatic fallback strategies\n\nBased on: test.py\nOptimized for: NVIDIA GeForce RTX 3050 Laptop GPU (4.3GB VRAM)\n\"\"\"\n\nimport os\nfrom share import *\nimport config\nimport config_rtx3050  # Import RTX 3050 optimizations\n\nimport cv2\nimport einops\nimport gradio as gr\nimport numpy as np\nimport torch\nimport random\nimport psutil\n\nfrom pytorch_lightning import seed_everything\nfrom annotator.util import resize_image\nfrom cldm.model import create_model, load_state_dict\nfrom cldm.ddim_haced_sag_step import DDIMSampler\nfrom lavis.models import load_model_and_preprocess\nfrom PIL import Image\nimport tqdm\n\nfrom ldm.models.autoencoder_train import AutoencoderKL\n\n# Import RTX 3050 optimized functions\nfrom config_rtx3050 import (\n    RTX3050_DEVICE, RTX3050_USE_FP16, RTX3050_INFERENCE_BATCH_SIZE, \n    RTX3050_DEFAULT_IMAGE_RESOLUTION, RTX3050MemoryManager, RTX3050AutocastManager,\n    clear_rtx3050_gpu_cache, get_rtx3050_optimal_batch_size, \n    get_rtx3050_optimal_image_resolution, get_rtx3050_device_info\n)\n\n# ============================================================================\n# RTX 3050 OPTIMIZED MODEL LOADING\n# ============================================================================\n\nprint(\"🎯 Loading CtrlColor with RTX 3050 optimizations...\")\ndevice_info = get_rtx3050_device_info()\nprint(f\"📊 Device: {device_info['name']} ({device_info['total_memory_gb']:.1f}GB)\")\n\n# Model paths\nckpt_path = \"./pretrained_models/main_model.ckpt\"\n\n# Load model with RTX 3050 optimizations\nwith RTX3050MemoryManager():\n    model = create_model('./models/cldm_v15_inpainting_infer1.yaml').cpu()\n    model.load_state_dict(load_state_dict(ckpt_path, location='cuda'), strict=False)\n    model = model.to(RTX3050_DEVICE)\n    \n    if RTX3050_USE_FP16:\n        model = model.half()\n        print(\"✅ Model loaded with FP16 precision\")\n\nddim_sampler = DDIMSampler(model)\n\n# Load BLIP model with optimizations\ndevice = torch.device(RTX3050_DEVICE)\ntry:\n    BLIP_model, vis_processors, _ = load_model_and_preprocess(\n        name=\"blip_caption\", model_type=\"base_coco\", is_eval=True, device=device\n    )\n    if RTX3050_USE_FP16:\n        BLIP_model = BLIP_model.half()\n    print(\"✅ BLIP model loaded\")\nexcept Exception as e:\n    print(f\"⚠️ BLIP loading failed: {e}\")\n    BLIP_model, vis_processors = None, None\n\n# Load VAE with optimizations\nvae_model_ckpt_path = \"./pretrained_models/content-guided_deformable_vae.ckpt\"\n\ndef load_vae_rtx3050():\n    \"\"\"Load VAE with RTX 3050 optimizations\"\"\"\n    init_config = {\n        \"embed_dim\": 4,\n        \"monitor\": \"val/rec_loss\",\n        \"ddconfig\": {\n            \"double_z\": True,\n            \"z_channels\": 4,\n            \"resolution\": 256,\n            \"in_channels\": 3,\n            \"out_ch\": 3,\n            \"ch\": 128,\n            \"ch_mult\": [1, 2, 4, 4],\n            \"num_res_blocks\": 2,\n            \"attn_resolutions\": [],\n            \"dropout\": 0.0,\n        },\n        \"lossconfig\": {\n            \"target\": \"ldm.modules.losses.LPIPSWithDiscriminator\",\n            \"params\": {\n                \"disc_start\": 501,\n                \"kl_weight\": 0,\n                \"disc_weight\": 0.025,\n                \"disc_factor\": 1.0\n            }\n        }\n    }\n    \n    with RTX3050MemoryManager():\n        vae = AutoencoderKL(**init_config)\n        vae.load_state_dict(load_state_dict(vae_model_ckpt_path, location='cuda'))\n        vae = vae.to(RTX3050_DEVICE)\n        \n        if RTX3050_USE_FP16:\n            vae = vae.half()\n        \n        return vae\n\nvae_model = load_vae_rtx3050()\nprint(\"✅ VAE model loaded with RTX 3050 optimizations\")\n\n# ============================================================================\n# RTX 3050 OPTIMIZED UTILITY FUNCTIONS\n# ============================================================================\n\ndef monitor_memory_usage():\n    \"\"\"Monitor GPU and RAM memory usage\"\"\"\n    if torch.cuda.is_available():\n        gpu_memory = torch.cuda.memory_allocated() / 1024**3\n        gpu_total = torch.cuda.get_device_properties(0).total_memory / 1024**3\n        gpu_percent = (gpu_memory / gpu_total) * 100\n    else:\n        gpu_memory = gpu_total = gpu_percent = 0\n    \n    ram_percent = psutil.virtual_memory().percent\n    \n    return {\n        'gpu_memory_gb': gpu_memory,\n        'gpu_total_gb': gpu_total,\n        'gpu_percent': gpu_percent,\n        'ram_percent': ram_percent\n    }\n\ndef adaptive_image_resolution(conservative=True):\n    \"\"\"Adaptively choose image resolution based on memory\"\"\"\n    memory_info = monitor_memory_usage()\n    \n    # If GPU memory usage is high, use conservative resolution\n    if memory_info['gpu_percent'] > 70 or memory_info['ram_percent'] > 85:\n        return 256  # Very conservative\n    elif conservative:\n        return RTX3050_DEFAULT_IMAGE_RESOLUTION  # 512\n    else:\n        return get_rtx3050_optimal_image_resolution(conservative=False)  # 768\n\ndef encode_mask_rtx3050(mask, masked_image):\n    \"\"\"RTX 3050 optimized mask encoding\"\"\"\n    with RTX3050AutocastManager():\n        mask = torch.nn.functional.interpolate(\n            mask, size=(mask.shape[2] // 8, mask.shape[3] // 8)\n        )\n        mask = mask.to(device=RTX3050_DEVICE)\n        \n        # Use autocast for VAE encoding\n        masked_image_latents = model.get_first_stage_encoding(\n            model.encode_first_stage(masked_image.to(RTX3050_DEVICE))\n        ).detach()\n        \n        return mask, masked_image_latents\n\n# ============================================================================\n# PRESERVED ORIGINAL FUNCTIONS (with RTX 3050 optimizations)\n# ============================================================================\n\ndef get_mask(input_image, hint_image):\n    \"\"\"Get mask from input and hint images (preserved from original)\"\"\"\n    mask = input_image.copy()\n    H, W, C = input_image.shape\n    \n    for i in range(H):\n        for j in range(W):\n            if input_image[i, j, 0] == hint_image[i, j, 0]:\n                mask[i, j, :] = 255.\n            else:\n                mask[i, j, :] = 0.\n    \n    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))\n    mask = cv2.morphologyEx(np.array(mask), cv2.MORPH_OPEN, kernel, iterations=1)\n    return mask\n\ndef prepare_mask_and_masked_image(image, mask):\n    \"\"\"Prepare mask and masked image (preserved from original with optimizations)\"\"\"\n    if isinstance(image, torch.Tensor):\n        if not isinstance(mask, torch.Tensor):\n            raise TypeError(f\"`image` is a torch.Tensor but `mask` (type: {type(mask)} is not\")\n\n        # Batch single image\n        if image.ndim == 3:\n            assert image.shape[0] == 3, \"Image outside a batch should be of shape (3, H, W)\"\n            image = image.unsqueeze(0)\n\n        # Batch and add channel dim for single mask\n        if mask.ndim == 2:\n            mask = mask.unsqueeze(0).unsqueeze(0)\n\n        # Batch single mask or add channel dim\n        if mask.ndim == 3:\n            if mask.shape[0] == 1:\n                mask = mask.unsqueeze(0)\n            else:\n                mask = mask.unsqueeze(1)\n\n        assert image.ndim == 4 and mask.ndim == 4, \"Image and Mask must have 4 dimensions\"\n        assert image.shape[-2:] == mask.shape[-2:], \"Image and Mask must have the same spatial dimensions\"\n        assert image.shape[0] == mask.shape[0], \"Image and Mask must have the same batch size\"\n\n        # Check image is in [-1, 1]\n        if image.min() < -1 or image.max() > 1:\n            raise ValueError(\"Image should be in [-1, 1] range\")\n\n        # Check mask is in [0, 1]\n        if mask.min() < 0 or mask.max() > 1:\n            raise ValueError(\"Mask should be in [0, 1] range\")\n\n        # Binarize mask\n        mask[mask < 0.5] = 0\n        mask[mask >= 0.5] = 1\n\n        # Image as float32\n        image = image.to(dtype=torch.float32)\n    elif isinstance(mask, torch.Tensor):\n        raise TypeError(f\"`mask` is a torch.Tensor but `image` (type: {type(image)} is not\")\n    else:\n        # preprocess image\n        if isinstance(image, (Image.Image, np.ndarray)):\n            image = [image]\n\n        if isinstance(image, list) and isinstance(image[0], Image.Image):\n            image = [np.array(i.convert(\"RGB\"))[None, :] for i in image]\n            image = np.concatenate(image, axis=0)\n        elif isinstance(image, list) and isinstance(image[0], np.ndarray):\n            image = np.concatenate([i[None, :] for i in image], axis=0)\n\n        image = image.transpose(0, 3, 1, 2)\n        image = torch.from_numpy(image).to(dtype=torch.float32) / 127.5 - 1.0\n\n        # preprocess mask\n        if isinstance(mask, (Image.Image, np.ndarray)):\n            mask = [mask]\n\n        if isinstance(mask, list) and isinstance(mask[0], Image.Image):\n            mask = np.concatenate([np.array(m.convert(\"L\"))[None, None, :] for m in mask], axis=0)\n            mask = mask.astype(np.float32) / 255.0\n        elif isinstance(mask, list) and isinstance(mask[0], np.ndarray):\n            mask = np.concatenate([m[None, None, :] for m in mask], axis=0)\n\n        mask[mask < 0.5] = 0\n        mask[mask >= 0.5] = 1\n        mask = torch.from_numpy(mask)\n\n    masked_image = image * (mask < 0.5)\n    return mask, masked_image\n\ndef is_gray_scale(img, threshold=10):\n    \"\"\"Check if image is grayscale (preserved from original)\"\"\"\n    img = Image.fromarray(img)\n    if len(img.getbands()) == 1:\n        return True\n    \n    img1 = np.asarray(img.getchannel(channel=0), dtype=np.int16)\n    img2 = np.asarray(img.getchannel(channel=1), dtype=np.int16)\n    img3 = np.asarray(img.getchannel(channel=2), dtype=np.int16)\n    \n    diff1 = (img1 - img2).var()\n    diff2 = (img2 - img3).var()\n    diff3 = (img3 - img1).var()\n    diff_sum = (diff1 + diff2 + diff3) / 3.0\n    \n    return diff_sum <= threshold\n\ndef randn_tensor(shape, generator=None, device=None, dtype=None, layout=None):\n    \"\"\"Create random tensor (preserved from original)\"\"\"\n    rand_device = device\n    batch_size = shape[0]\n\n    layout = layout or torch.strided\n    device = device or torch.device(\"cpu\")\n\n    if generator is not None:\n        gen_device_type = generator.device.type if not isinstance(generator, list) else generator[0].device.type\n        if gen_device_type != device.type and gen_device_type == \"cpu\":\n            rand_device = \"cpu\"\n        elif gen_device_type != device.type and gen_device_type == \"cuda\":\n            raise ValueError(f\"Cannot generate a {device} tensor from a generator of type {gen_device_type}.\")\n\n    if isinstance(generator, list) and len(generator) == 1:\n        generator = generator[0]\n\n    if isinstance(generator, list):\n        shape = (1,) + shape[1:]\n        latents = [\n            torch.randn(shape, generator=generator[i], device=rand_device, dtype=dtype, layout=layout)\n            for i in range(batch_size)\n        ]\n        latents = torch.cat(latents, dim=0).to(device)\n    else:\n        latents = torch.randn(shape, generator=generator, device=rand_device, dtype=dtype, layout=layout).to(device)\n\n    return latents\n\n# Set generator seed\ngenerator = torch.manual_seed(859311133)\n"}
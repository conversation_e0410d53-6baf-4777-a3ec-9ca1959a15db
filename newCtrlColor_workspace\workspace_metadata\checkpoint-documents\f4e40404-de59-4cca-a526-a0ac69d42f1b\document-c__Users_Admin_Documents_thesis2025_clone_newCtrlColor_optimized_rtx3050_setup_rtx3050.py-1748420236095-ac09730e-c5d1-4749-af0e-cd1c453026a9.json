{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/newCtrlColor/optimized_rtx3050/setup_rtx3050.py"}, "originalCode": "#!/usr/bin/env python3\n\"\"\"\nRTX 3050 Setup Script for CtrlColor\n\nThis script automatically sets up the RTX 3050 optimized environment:\n- Copies required files from original codebase\n- Installs optimized dependencies\n- Validates RTX 3050 compatibility\n- Launches optimized interface\n\nUsage:\n    python setup_rtx3050.py\n\"\"\"\n\nimport os\nimport sys\nimport shutil\nimport subprocess\nimport torch\nfrom pathlib import Path\n\ndef print_header():\n    \"\"\"Print setup header\"\"\"\n    print(\"🎯\" + \"=\"*60)\n    print(\"🚀 RTX 3050 OPTIMIZED CTRLCOLOR SETUP\")\n    print(\"🎯\" + \"=\"*60)\n    print(\"Setting up CtrlColor with RTX 3050 optimizations...\")\n    print()\n\ndef check_rtx3050():\n    \"\"\"Check if RTX 3050 is available\"\"\"\n    print(\"🔍 Checking RTX 3050 compatibility...\")\n    \n    if not torch.cuda.is_available():\n        print(\"❌ CUDA not available!\")\n        print(\"   Please install CUDA-compatible PyTorch\")\n        return False\n    \n    device_name = torch.cuda.get_device_name(0)\n    total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3\n    \n    print(f\"✅ GPU detected: {device_name}\")\n    print(f\"✅ VRAM: {total_memory:.1f}GB\")\n    \n    if \"3050\" in device_name:\n        print(\"🎯 RTX 3050 detected - perfect match!\")\n    elif total_memory < 6.0:\n        print(\"🎯 Low VRAM GPU detected - RTX 3050 optimizations will help!\")\n    else:\n        print(\"ℹ️ High VRAM GPU detected - optimizations still beneficial\")\n    \n    return True\n\ndef copy_required_files():\n    \"\"\"Copy required files from original codebase\"\"\"\n    print(\"\\n📁 Copying required files...\")\n    \n    # Get current directory and parent directory\n    current_dir = Path(__file__).parent\n    parent_dir = current_dir.parent\n    \n    # Files and directories to copy\n    required_items = [\n        'models',\n        'pretrained_models', \n        'annotator',\n        'cldm',\n        'ldm',\n        'share.py',\n        'config.py'\n    ]\n    \n    copied_count = 0\n    \n    for item in required_items:\n        source = parent_dir / item\n        target = current_dir / item\n        \n        if source.exists():\n            if source.is_dir():\n                if target.exists():\n                    shutil.rmtree(target)\n                shutil.copytree(source, target)\n                print(f\"✅ Copied directory: {item}\")\n            else:\n                shutil.copy2(source, target)\n                print(f\"✅ Copied file: {item}\")\n            copied_count += 1\n        else:\n            print(f\"⚠️ Not found: {item} (may need manual download)\")\n    \n    print(f\"\\n📊 Copied {copied_count}/{len(required_items)} items\")\n    return copied_count > 0\n\ndef install_dependencies():\n    \"\"\"Install RTX 3050 optimized dependencies\"\"\"\n    print(\"\\n📦 Installing RTX 3050 optimized dependencies...\")\n    \n    # Core dependencies\n    core_packages = [\n        \"torch>=1.12.0\",\n        \"torchvision>=0.13.0\", \n        \"gradio>=3.35.0\",\n        \"opencv-python>=4.6.0\",\n        \"pillow>=9.0.0\",\n        \"numpy>=1.21.0\",\n        \"psutil>=5.8.0\",\n        \"tqdm>=4.64.0\"\n    ]\n    \n    # Optional dependencies\n    optional_packages = [\n        \"pytorch-lightning>=1.7.0\",\n        \"transformers>=4.20.0\",\n        \"diffusers>=0.15.0\",\n        \"einops>=0.4.0\"\n    ]\n    \n    print(\"Installing core packages...\")\n    for package in core_packages:\n        try:\n            subprocess.run([sys.executable, '-m', 'pip', 'install', package], \n                         check=True, capture_output=True)\n            print(f\"✅ {package}\")\n        except subprocess.CalledProcessError:\n            print(f\"❌ Failed: {package}\")\n    \n    print(\"\\nInstalling optional packages...\")\n    for package in optional_packages:\n        try:\n            subprocess.run([sys.executable, '-m', 'pip', 'install', package], \n                         check=True, capture_output=True)\n            print(f\"✅ {package}\")\n        except subprocess.CalledProcessError:\n            print(f\"⚠️ Optional: {package} (may need manual install)\")\n    \n    print(\"✅ Dependency installation complete!\")\n\ndef validate_setup():\n    \"\"\"Validate the RTX 3050 setup\"\"\"\n    print(\"\\n🔍 Validating RTX 3050 setup...\")\n    \n    try:\n        # Test imports\n        import torch\n        import cv2\n        import numpy as np\n        import gradio as gr\n        print(\"✅ Core imports successful\")\n        \n        # Test CUDA\n        if torch.cuda.is_available():\n            print(\"✅ CUDA available\")\n            \n            # Test FP16\n            if torch.cuda.is_available():\n                test_tensor = torch.randn(1, 3, 256, 256).cuda().half()\n                print(\"✅ FP16 support working\")\n            \n        # Test config import\n        try:\n            from config_rtx3050 import get_device_info\n            device_info = get_device_info()\n            print(f\"✅ RTX 3050 config loaded: {device_info['name']}\")\n        except ImportError:\n            print(\"⚠️ RTX 3050 config not found (expected in same directory)\")\n        \n        print(\"✅ Setup validation complete!\")\n        return True\n        \n    except ImportError as e:\n        print(f\"❌ Import error: {e}\")\n        return False\n\ndef create_launch_script():\n    \"\"\"Create convenient launch script\"\"\"\n    print(\"\\n📝 Creating launch script...\")\n    \n    launch_script = \"\"\"#!/usr/bin/env python3\n# RTX 3050 Optimized CtrlColor Launcher\n\nimport sys\nimport os\n\n# Add current directory to path\nsys.path.insert(0, os.path.dirname(__file__))\n\ntry:\n    from test_rtx3050 import create_rtx3050_interface\n    \n    print(\"🚀 Launching RTX 3050 optimized CtrlColor...\")\n    interface = create_rtx3050_interface()\n    interface.launch(\n        server_port=7860,\n        share=False,\n        inbrowser=True\n    )\n    \nexcept ImportError as e:\n    print(f\"❌ Import error: {e}\")\n    print(\"Please run setup_rtx3050.py first\")\nexcept Exception as e:\n    print(f\"❌ Launch error: {e}\")\n\"\"\"\n    \n    with open(\"launch_rtx3050.py\", \"w\") as f:\n        f.write(launch_script)\n    \n    print(\"✅ Created launch_rtx3050.py\")\n\ndef print_summary():\n    \"\"\"Print setup summary\"\"\"\n    print(\"\\n🎉\" + \"=\"*60)\n    print(\"🎉 RTX 3050 SETUP COMPLETE!\")\n    print(\"🎉\" + \"=\"*60)\n    print()\n    print(\"📋 What was set up:\")\n    print(\"✅ RTX 3050 optimized configuration\")\n    print(\"✅ Memory management (85% VRAM usage)\")\n    print(\"✅ FP16 mixed precision (50% memory savings)\")\n    print(\"✅ Optimal batch sizes (1-2 for inference)\")\n    print(\"✅ Adaptive resolution (up to 768px)\")\n    print(\"✅ Required dependencies installed\")\n    print(\"✅ Launch script created\")\n    print()\n    print(\"🚀 How to run:\")\n    print(\"   python launch_rtx3050.py\")\n    print(\"   OR\")\n    print(\"   python test_rtx3050.py\")\n    print()\n    print(\"📊 Expected performance:\")\n    print(\"   - Memory usage: ~3.6GB / 4.3GB (85%)\")\n    print(\"   - Max resolution: 768x768\")\n    print(\"   - Batch size: 1-2 samples\")\n    print(\"   - Speed: 0.4-0.7x faster with FP16\")\n    print()\n    print(\"🎯 Your RTX 3050 is now optimized for CtrlColor!\")\n\ndef main():\n    \"\"\"Main setup function\"\"\"\n    print_header()\n    \n    # Step 1: Check RTX 3050\n    if not check_rtx3050():\n        print(\"❌ Setup aborted - GPU compatibility issues\")\n        return False\n    \n    # Step 2: Copy files\n    if not copy_required_files():\n        print(\"⚠️ Some files missing - may need manual setup\")\n    \n    # Step 3: Install dependencies\n    install_dependencies()\n    \n    # Step 4: Validate setup\n    if not validate_setup():\n        print(\"⚠️ Setup validation failed - check dependencies\")\n    \n    # Step 5: Create launch script\n    create_launch_script()\n    \n    # Step 6: Print summary\n    print_summary()\n    \n    return True\n\nif __name__ == \"__main__\":\n    try:\n        success = main()\n        if success:\n            # Ask if user wants to launch immediately\n            response = input(\"\\n🚀 Launch RTX 3050 optimized interface now? (y/n): \")\n            if response.lower() == 'y':\n                print(\"Launching...\")\n                try:\n                    from test_rtx3050 import create_rtx3050_interface\n                    interface = create_rtx3050_interface()\n                    interface.launch(server_port=7860, share=False, inbrowser=True)\n                except Exception as e:\n                    print(f\"❌ Launch failed: {e}\")\n                    print(\"Try running: python launch_rtx3050.py\")\n        else:\n            print(\"❌ Setup failed!\")\n            sys.exit(1)\n            \n    except KeyboardInterrupt:\n        print(\"\\n⚠️ Setup interrupted by user\")\n    except Exception as e:\n        print(f\"\\n❌ Setup failed with error: {e}\")\n        sys.exit(1)\n", "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nRTX 3050 Setup Script for CtrlColor\n\nThis script automatically sets up the RTX 3050 optimized environment:\n- Copies required files from original codebase\n- Installs optimized dependencies\n- Validates RTX 3050 compatibility\n- Launches optimized interface\n\nUsage:\n    python setup_rtx3050.py\n\"\"\"\n\nimport os\nimport sys\nimport shutil\nimport subprocess\nimport torch\nfrom pathlib import Path\n\ndef print_header():\n    \"\"\"Print setup header\"\"\"\n    print(\"🎯\" + \"=\"*60)\n    print(\"🚀 RTX 3050 OPTIMIZED CTRLCOLOR SETUP\")\n    print(\"🎯\" + \"=\"*60)\n    print(\"Setting up CtrlColor with RTX 3050 optimizations...\")\n    print()\n\ndef check_rtx3050():\n    \"\"\"Check if RTX 3050 is available\"\"\"\n    print(\"🔍 Checking RTX 3050 compatibility...\")\n    \n    if not torch.cuda.is_available():\n        print(\"❌ CUDA not available!\")\n        print(\"   Please install CUDA-compatible PyTorch\")\n        return False\n    \n    device_name = torch.cuda.get_device_name(0)\n    total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3\n    \n    print(f\"✅ GPU detected: {device_name}\")\n    print(f\"✅ VRAM: {total_memory:.1f}GB\")\n    \n    if \"3050\" in device_name:\n        print(\"🎯 RTX 3050 detected - perfect match!\")\n    elif total_memory < 6.0:\n        print(\"🎯 Low VRAM GPU detected - RTX 3050 optimizations will help!\")\n    else:\n        print(\"ℹ️ High VRAM GPU detected - optimizations still beneficial\")\n    \n    return True\n\ndef copy_required_files():\n    \"\"\"Copy required files from original codebase\"\"\"\n    print(\"\\n📁 Copying required files...\")\n    \n    # Get current directory and parent directory\n    current_dir = Path(__file__).parent\n    parent_dir = current_dir.parent\n    \n    # Files and directories to copy\n    required_items = [\n        'models',\n        'pretrained_models', \n        'annotator',\n        'cldm',\n        'ldm',\n        'share.py',\n        'config.py'\n    ]\n    \n    copied_count = 0\n    \n    for item in required_items:\n        source = parent_dir / item\n        target = current_dir / item\n        \n        if source.exists():\n            if source.is_dir():\n                if target.exists():\n                    shutil.rmtree(target)\n                shutil.copytree(source, target)\n                print(f\"✅ Copied directory: {item}\")\n            else:\n                shutil.copy2(source, target)\n                print(f\"✅ Copied file: {item}\")\n            copied_count += 1\n        else:\n            print(f\"⚠️ Not found: {item} (may need manual download)\")\n    \n    print(f\"\\n📊 Copied {copied_count}/{len(required_items)} items\")\n    return copied_count > 0\n\ndef install_dependencies():\n    \"\"\"Install RTX 3050 optimized dependencies\"\"\"\n    print(\"\\n📦 Installing RTX 3050 optimized dependencies...\")\n    \n    # Core dependencies\n    core_packages = [\n        \"torch>=1.12.0\",\n        \"torchvision>=0.13.0\", \n        \"gradio>=3.35.0\",\n        \"opencv-python>=4.6.0\",\n        \"pillow>=9.0.0\",\n        \"numpy>=1.21.0\",\n        \"psutil>=5.8.0\",\n        \"tqdm>=4.64.0\"\n    ]\n    \n    # Optional dependencies\n    optional_packages = [\n        \"pytorch-lightning>=1.7.0\",\n        \"transformers>=4.20.0\",\n        \"diffusers>=0.15.0\",\n        \"einops>=0.4.0\"\n    ]\n    \n    print(\"Installing core packages...\")\n    for package in core_packages:\n        try:\n            subprocess.run([sys.executable, '-m', 'pip', 'install', package], \n                         check=True, capture_output=True)\n            print(f\"✅ {package}\")\n        except subprocess.CalledProcessError:\n            print(f\"❌ Failed: {package}\")\n    \n    print(\"\\nInstalling optional packages...\")\n    for package in optional_packages:\n        try:\n            subprocess.run([sys.executable, '-m', 'pip', 'install', package], \n                         check=True, capture_output=True)\n            print(f\"✅ {package}\")\n        except subprocess.CalledProcessError:\n            print(f\"⚠️ Optional: {package} (may need manual install)\")\n    \n    print(\"✅ Dependency installation complete!\")\n\ndef validate_setup():\n    \"\"\"Validate the RTX 3050 setup\"\"\"\n    print(\"\\n🔍 Validating RTX 3050 setup...\")\n    \n    try:\n        # Test imports\n        import torch\n        import cv2\n        import numpy as np\n        import gradio as gr\n        print(\"✅ Core imports successful\")\n        \n        # Test CUDA\n        if torch.cuda.is_available():\n            print(\"✅ CUDA available\")\n            \n            # Test FP16\n            if torch.cuda.is_available():\n                test_tensor = torch.randn(1, 3, 256, 256).cuda().half()\n                print(\"✅ FP16 support working\")\n            \n        # Test config import\n        try:\n            from config_rtx3050 import get_device_info\n            device_info = get_device_info()\n            print(f\"✅ RTX 3050 config loaded: {device_info['name']}\")\n        except ImportError:\n            print(\"⚠️ RTX 3050 config not found (expected in same directory)\")\n        \n        print(\"✅ Setup validation complete!\")\n        return True\n        \n    except ImportError as e:\n        print(f\"❌ Import error: {e}\")\n        return False\n\ndef create_launch_script():\n    \"\"\"Create convenient launch script\"\"\"\n    print(\"\\n📝 Creating launch script...\")\n    \n    launch_script = \"\"\"#!/usr/bin/env python3\n# RTX 3050 Optimized CtrlColor Launcher\n\nimport sys\nimport os\n\n# Add current directory to path\nsys.path.insert(0, os.path.dirname(__file__))\n\ntry:\n    from test_rtx3050 import create_rtx3050_interface\n    \n    print(\"🚀 Launching RTX 3050 optimized CtrlColor...\")\n    interface = create_rtx3050_interface()\n    interface.launch(\n        server_port=7860,\n        share=False,\n        inbrowser=True\n    )\n    \nexcept ImportError as e:\n    print(f\"❌ Import error: {e}\")\n    print(\"Please run setup_rtx3050.py first\")\nexcept Exception as e:\n    print(f\"❌ Launch error: {e}\")\n\"\"\"\n    \n    with open(\"launch_rtx3050.py\", \"w\") as f:\n        f.write(launch_script)\n    \n    print(\"✅ Created launch_rtx3050.py\")\n\ndef print_summary():\n    \"\"\"Print setup summary\"\"\"\n    print(\"\\n🎉\" + \"=\"*60)\n    print(\"🎉 RTX 3050 SETUP COMPLETE!\")\n    print(\"🎉\" + \"=\"*60)\n    print()\n    print(\"📋 What was set up:\")\n    print(\"✅ RTX 3050 optimized configuration\")\n    print(\"✅ Memory management (85% VRAM usage)\")\n    print(\"✅ FP16 mixed precision (50% memory savings)\")\n    print(\"✅ Optimal batch sizes (1-2 for inference)\")\n    print(\"✅ Adaptive resolution (up to 768px)\")\n    print(\"✅ Required dependencies installed\")\n    print(\"✅ Launch script created\")\n    print()\n    print(\"🚀 How to run:\")\n    print(\"   python launch_rtx3050.py\")\n    print(\"   OR\")\n    print(\"   python test_rtx3050.py\")\n    print()\n    print(\"📊 Expected performance:\")\n    print(\"   - Memory usage: ~3.6GB / 4.3GB (85%)\")\n    print(\"   - Max resolution: 768x768\")\n    print(\"   - Batch size: 1-2 samples\")\n    print(\"   - Speed: 0.4-0.7x faster with FP16\")\n    print()\n    print(\"🎯 Your RTX 3050 is now optimized for CtrlColor!\")\n\ndef main():\n    \"\"\"Main setup function\"\"\"\n    print_header()\n    \n    # Step 1: Check RTX 3050\n    if not check_rtx3050():\n        print(\"❌ Setup aborted - GPU compatibility issues\")\n        return False\n    \n    # Step 2: Copy files\n    if not copy_required_files():\n        print(\"⚠️ Some files missing - may need manual setup\")\n    \n    # Step 3: Install dependencies\n    install_dependencies()\n    \n    # Step 4: Validate setup\n    if not validate_setup():\n        print(\"⚠️ Setup validation failed - check dependencies\")\n    \n    # Step 5: Create launch script\n    create_launch_script()\n    \n    # Step 6: Print summary\n    print_summary()\n    \n    return True\n\nif __name__ == \"__main__\":\n    try:\n        success = main()\n        if success:\n            # Ask if user wants to launch immediately\n            response = input(\"\\n🚀 Launch RTX 3050 optimized interface now? (y/n): \")\n            if response.lower() == 'y':\n                print(\"Launching...\")\n                try:\n                    from test_rtx3050 import create_rtx3050_interface\n                    interface = create_rtx3050_interface()\n                    interface.launch(server_port=7860, share=False, inbrowser=True)\n                except Exception as e:\n                    print(f\"❌ Launch failed: {e}\")\n                    print(\"Try running: python launch_rtx3050.py\")\n        else:\n            print(\"❌ Setup failed!\")\n            sys.exit(1)\n            \n    except KeyboardInterrupt:\n        print(\"\\n⚠️ Setup interrupted by user\")\n    except Exception as e:\n        print(f\"\\n❌ Setup failed with error: {e}\")\n        sys.exit(1)\n"}
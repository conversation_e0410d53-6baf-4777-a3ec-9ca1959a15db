{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/newCtrlColor/test_absolute_imports.py"}, "originalCode": "\"\"\"\nTest Absolute Imports for CtrlColor\n\nThis script tests that all absolute imports work correctly without needing sys.path manipulation.\nRun this to verify the import structure is working properly.\n\"\"\"\n\nimport sys\nimport os\n\ndef test_core_imports():\n    \"\"\"Test core CtrlColor module imports\"\"\"\n    print(\"🧪 Testing Core Module Imports...\")\n    \n    try:\n        from full.losses.contextual_loss import ContextualLoss\n        print(\"✅ full.losses.contextual_loss imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.losses.contextual_loss failed: {e}\")\n    \n    try:\n        from full.losses.grayscale_loss import GrayscaleLoss\n        print(\"✅ full.losses.grayscale_loss imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.losses.grayscale_loss failed: {e}\")\n    \n    try:\n        from full.losses.exemplar_loss import ExemplarLoss\n        print(\"✅ full.losses.exemplar_loss imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.losses.exemplar_loss failed: {e}\")\n    \n    try:\n        from full.modules.exemplar_processor import ExemplarProcessor\n        print(\"✅ full.modules.exemplar_processor imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.modules.exemplar_processor failed: {e}\")\n    \n    try:\n        from full.cldm.exemplar_cldm import ExemplarControlLDM\n        print(\"✅ full.cldm.exemplar_cldm imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.cldm.exemplar_cldm failed: {e}\")\n    \n    try:\n        from full.data.data_processor import LabColorProcessor\n        print(\"✅ full.data.data_processor imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.data.data_processor failed: {e}\")\n    \n    try:\n        from full.evaluation.metrics import MetricsCalculator\n        print(\"✅ full.evaluation.metrics imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.evaluation.metrics failed: {e}\")\n\ndef test_training_imports():\n    \"\"\"Test training module imports\"\"\"\n    print(\"\\n🏋️ Testing Training Module Imports...\")\n    \n    try:\n        from full.training.base_trainer import BaseCtrlColorTrainer\n        print(\"✅ full.training.base_trainer imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.training.base_trainer failed: {e}\")\n    \n    try:\n        from full.training.train_stage1_sd import Stage1SDTrainer\n        print(\"✅ full.training.train_stage1_sd imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.training.train_stage1_sd failed: {e}\")\n\ndef test_device_optimization_imports():\n    \"\"\"Test device optimization module imports\"\"\"\n    print(\"\\n⚡ Testing Device Optimization Imports...\")\n    \n    try:\n        from full.device_optimization.optimized_inference import OptimizedCtrlColorInference\n        print(\"✅ full.device_optimization.optimized_inference imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.device_optimization.optimized_inference failed: {e}\")\n    \n    try:\n        from full.device_optimization.memory_efficient_training import MemoryEfficientTrainer\n        print(\"✅ full.device_optimization.memory_efficient_training imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.device_optimization.memory_efficient_training failed: {e}\")\n    \n    try:\n        from full.device_optimization.performance_monitor import RTX3050PerformanceMonitor\n        print(\"✅ full.device_optimization.performance_monitor imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.device_optimization.performance_monitor failed: {e}\")\n\ndef test_ui_imports():\n    \"\"\"Test UI module imports\"\"\"\n    print(\"\\n🖥️ Testing UI Module Imports...\")\n    \n    try:\n        from full.ui.advanced_interface import AdvancedCtrlColorInterface\n        print(\"✅ full.ui.advanced_interface imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.ui.advanced_interface failed: {e}\")\n\ndef test_application_imports():\n    \"\"\"Test application module imports\"\"\"\n    print(\"\\n🎨 Testing Application Module Imports...\")\n    \n    try:\n        from full.applications.video_colorization import VideoColorizer\n        print(\"✅ full.applications.video_colorization imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.applications.video_colorization failed: {e}\")\n\ndef test_script_imports():\n    \"\"\"Test script module imports\"\"\"\n    print(\"\\n📜 Testing Script Module Imports...\")\n    \n    try:\n        from full.scripts.reproduce_paper_results import PaperReproducer\n        print(\"✅ full.scripts.reproduce_paper_results imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.scripts.reproduce_paper_results failed: {e}\")\n\ndef test_pytorch_imports():\n    \"\"\"Test PyTorch availability\"\"\"\n    print(\"\\n🔥 Testing PyTorch Imports...\")\n    \n    try:\n        import torch\n        print(f\"✅ PyTorch {torch.__version__} imported successfully\")\n        \n        if torch.cuda.is_available():\n            print(f\"✅ CUDA available: {torch.cuda.device_count()} device(s)\")\n            for i in range(torch.cuda.device_count()):\n                props = torch.cuda.get_device_properties(i)\n                print(f\"   - GPU {i}: {props.name} ({props.total_memory / 1e9:.1f}GB)\")\n        else:\n            print(\"⚠️ CUDA not available\")\n        \n    except ImportError as e:\n        print(f\"❌ PyTorch import failed: {e}\")\n\ndef main():\n    \"\"\"Main test function\"\"\"\n    print(\"🚀 CTRLCOLOR ABSOLUTE IMPORT TEST\")\n    print(\"=\"*50)\n    print(f\"Python version: {sys.version}\")\n    print(f\"Working directory: {os.getcwd()}\")\n    print(f\"Python path entries: {len(sys.path)}\")\n    print(\"=\"*50)\n    \n    # Run all import tests\n    test_pytorch_imports()\n    test_core_imports()\n    test_training_imports()\n    test_device_optimization_imports()\n    test_ui_imports()\n    test_application_imports()\n    test_script_imports()\n    \n    print(\"\\n\" + \"=\"*50)\n    print(\"✅ IMPORT TEST COMPLETED\")\n    print(\"=\"*50)\n    \n    print(\"\\n📋 Summary:\")\n    print(\"- All imports use absolute paths (full.module.submodule)\")\n    print(\"- No sys.path manipulation needed\")\n    print(\"- Clean and maintainable import structure\")\n    print(\"- Ready for production use\")\n    \n    print(\"\\n🚀 Next steps:\")\n    print(\"1. Run: python run_rtx3050_test.py\")\n    print(\"2. Test your RTX 3050 optimizations\")\n    print(\"3. Start using CtrlColor with optimized settings\")\n\nif __name__ == \"__main__\":\n    main()\n", "modifiedCode": "\"\"\"\nTest Absolute Imports for CtrlColor\n\nThis script tests that all absolute imports work correctly without needing sys.path manipulation.\nRun this to verify the import structure is working properly.\n\"\"\"\n\nimport sys\nimport os\n\ndef test_core_imports():\n    \"\"\"Test core CtrlColor module imports\"\"\"\n    print(\"🧪 Testing Core Module Imports...\")\n    \n    try:\n        from full.losses.contextual_loss import ContextualLoss\n        print(\"✅ full.losses.contextual_loss imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.losses.contextual_loss failed: {e}\")\n    \n    try:\n        from full.losses.grayscale_loss import GrayscaleLoss\n        print(\"✅ full.losses.grayscale_loss imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.losses.grayscale_loss failed: {e}\")\n    \n    try:\n        from full.losses.exemplar_loss import ExemplarLoss\n        print(\"✅ full.losses.exemplar_loss imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.losses.exemplar_loss failed: {e}\")\n    \n    try:\n        from full.modules.exemplar_processor import ExemplarProcessor\n        print(\"✅ full.modules.exemplar_processor imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.modules.exemplar_processor failed: {e}\")\n    \n    try:\n        from full.cldm.exemplar_cldm import ExemplarControlLDM\n        print(\"✅ full.cldm.exemplar_cldm imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.cldm.exemplar_cldm failed: {e}\")\n    \n    try:\n        from full.data.data_processor import LabColorProcessor\n        print(\"✅ full.data.data_processor imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.data.data_processor failed: {e}\")\n    \n    try:\n        from full.evaluation.metrics import MetricsCalculator\n        print(\"✅ full.evaluation.metrics imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.evaluation.metrics failed: {e}\")\n\ndef test_training_imports():\n    \"\"\"Test training module imports\"\"\"\n    print(\"\\n🏋️ Testing Training Module Imports...\")\n    \n    try:\n        from full.training.base_trainer import BaseCtrlColorTrainer\n        print(\"✅ full.training.base_trainer imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.training.base_trainer failed: {e}\")\n    \n    try:\n        from full.training.train_stage1_sd import Stage1SDTrainer\n        print(\"✅ full.training.train_stage1_sd imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.training.train_stage1_sd failed: {e}\")\n\ndef test_device_optimization_imports():\n    \"\"\"Test device optimization module imports\"\"\"\n    print(\"\\n⚡ Testing Device Optimization Imports...\")\n    \n    try:\n        from full.device_optimization.optimized_inference import OptimizedCtrlColorInference\n        print(\"✅ full.device_optimization.optimized_inference imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.device_optimization.optimized_inference failed: {e}\")\n    \n    try:\n        from full.device_optimization.memory_efficient_training import MemoryEfficientTrainer\n        print(\"✅ full.device_optimization.memory_efficient_training imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.device_optimization.memory_efficient_training failed: {e}\")\n    \n    try:\n        from full.device_optimization.performance_monitor import RTX3050PerformanceMonitor\n        print(\"✅ full.device_optimization.performance_monitor imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.device_optimization.performance_monitor failed: {e}\")\n\ndef test_ui_imports():\n    \"\"\"Test UI module imports\"\"\"\n    print(\"\\n🖥️ Testing UI Module Imports...\")\n    \n    try:\n        from full.ui.advanced_interface import AdvancedCtrlColorInterface\n        print(\"✅ full.ui.advanced_interface imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.ui.advanced_interface failed: {e}\")\n\ndef test_application_imports():\n    \"\"\"Test application module imports\"\"\"\n    print(\"\\n🎨 Testing Application Module Imports...\")\n    \n    try:\n        from full.applications.video_colorization import VideoColorizer\n        print(\"✅ full.applications.video_colorization imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.applications.video_colorization failed: {e}\")\n\ndef test_script_imports():\n    \"\"\"Test script module imports\"\"\"\n    print(\"\\n📜 Testing Script Module Imports...\")\n    \n    try:\n        from full.scripts.reproduce_paper_results import PaperReproducer\n        print(\"✅ full.scripts.reproduce_paper_results imported successfully\")\n    except ImportError as e:\n        print(f\"❌ full.scripts.reproduce_paper_results failed: {e}\")\n\ndef test_pytorch_imports():\n    \"\"\"Test PyTorch availability\"\"\"\n    print(\"\\n🔥 Testing PyTorch Imports...\")\n    \n    try:\n        import torch\n        print(f\"✅ PyTorch {torch.__version__} imported successfully\")\n        \n        if torch.cuda.is_available():\n            print(f\"✅ CUDA available: {torch.cuda.device_count()} device(s)\")\n            for i in range(torch.cuda.device_count()):\n                props = torch.cuda.get_device_properties(i)\n                print(f\"   - GPU {i}: {props.name} ({props.total_memory / 1e9:.1f}GB)\")\n        else:\n            print(\"⚠️ CUDA not available\")\n        \n    except ImportError as e:\n        print(f\"❌ PyTorch import failed: {e}\")\n\ndef main():\n    \"\"\"Main test function\"\"\"\n    print(\"🚀 CTRLCOLOR ABSOLUTE IMPORT TEST\")\n    print(\"=\"*50)\n    print(f\"Python version: {sys.version}\")\n    print(f\"Working directory: {os.getcwd()}\")\n    print(f\"Python path entries: {len(sys.path)}\")\n    print(\"=\"*50)\n    \n    # Run all import tests\n    test_pytorch_imports()\n    test_core_imports()\n    test_training_imports()\n    test_device_optimization_imports()\n    test_ui_imports()\n    test_application_imports()\n    test_script_imports()\n    \n    print(\"\\n\" + \"=\"*50)\n    print(\"✅ IMPORT TEST COMPLETED\")\n    print(\"=\"*50)\n    \n    print(\"\\n📋 Summary:\")\n    print(\"- All imports use absolute paths (full.module.submodule)\")\n    print(\"- No sys.path manipulation needed\")\n    print(\"- Clean and maintainable import structure\")\n    print(\"- Ready for production use\")\n    \n    print(\"\\n🚀 Next steps:\")\n    print(\"1. Run: python run_rtx3050_test.py\")\n    print(\"2. Test your RTX 3050 optimizations\")\n    print(\"3. Start using CtrlColor with optimized settings\")\n\nif __name__ == \"__main__\":\n    main()\n"}
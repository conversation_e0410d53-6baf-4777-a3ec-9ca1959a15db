{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\newCtrlColor\\test.py"}, "originalCode": "import os\r\nimport random\r\n\r\nimport cv2\r\nimport einops\r\nimport gradio as gr\r\nimport numpy as np\r\nimport torch\r\nimport tqdm\r\nfrom lavis.models import load_model_and_preprocess\r\nfrom PIL import Image\r\nfrom pytorch_lightning import seed_everything\r\n\r\nimport config\r\nfrom annotator.util import resize_image\r\nfrom cldm.ddim_haced_sag_step import DDIMSampler\r\nfrom cldm.model import create_model, load_state_dict\r\nfrom ldm.models.autoencoder_train import AutoencoderKL\r\nfrom share import *\r\n\r\n# write images to logs folder\r\nos.makedirs(\"logs\", exist_ok=True)\r\n\r\nckpt_path = \"./pretrained_models/main_model.ckpt\"\r\n\r\nmodel = create_model(\"./models/cldm_v15_inpainting_infer1.yaml\").cpu()\r\nmodel.load_state_dict(load_state_dict(ckpt_path, location=\"cuda\"), strict=False)\r\nmodel = model.cuda()\r\n\r\nddim_sampler = DDIMSampler(model)\r\n\r\n\r\ndevice = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\r\nBLIP_model, vis_processors, _ = load_model_and_preprocess(\r\n    name=\"blip_caption\", model_type=\"base_coco\", is_eval=True, device=device\r\n)\r\n\r\nvae_model_ckpt_path = \"./pretrained_models/content-guided_deformable_vae.ckpt\"\r\n\r\n\r\ndef load_vae():\r\n    init_config = {\r\n        \"embed_dim\": 4,\r\n        \"monitor\": \"val/rec_loss\",\r\n        \"ddconfig\": {\r\n            \"double_z\": True,\r\n            \"z_channels\": 4,\r\n            \"resolution\": 256,\r\n            \"in_channels\": 3,\r\n            \"out_ch\": 3,\r\n            \"ch\": 128,\r\n            \"ch_mult\": [1, 2, 4, 4],\r\n            \"num_res_blocks\": 2,\r\n            \"attn_resolutions\": [],\r\n            \"dropout\": 0.0,\r\n        },\r\n        \"lossconfig\": {\r\n            \"target\": \"ldm.modules.losses.LPIPSWithDiscriminator\",\r\n            \"params\": {\r\n                \"disc_start\": 501,\r\n                \"kl_weight\": 0,\r\n                \"disc_weight\": 0.025,\r\n                \"disc_factor\": 1.0,\r\n            },\r\n        },\r\n    }\r\n    vae = AutoencoderKL(**init_config)\r\n    vae.load_state_dict(load_state_dict(vae_model_ckpt_path, location=\"cuda\"))\r\n    vae = vae.cuda()\r\n    return vae\r\n\r\n\r\nvae_model = load_vae()\r\n\r\n\r\ndef encode_mask(mask, masked_image):\r\n    mask = torch.nn.functional.interpolate(\r\n        mask, size=(mask.shape[2] // 8, mask.shape[3] // 8)\r\n    )\r\n    # mask=torch.cat([mask] * 2) #if do_classifier_free_guidance else mask\r\n    mask = mask.to(device=\"cuda\")\r\n    # do_classifier_free_guidance=False\r\n    masked_image_latents = model.get_first_stage_encoding(\r\n        model.encode_first_stage(masked_image.cuda())\r\n    ).detach()\r\n    return mask, masked_image_latents\r\n\r\n\r\ndef get_mask(input_image, hint_image):\r\n    mask = input_image.copy()\r\n    H, W, C = input_image.shape\r\n    for i in range(H):\r\n        for j in range(W):\r\n            if input_image[i, j, 0] == hint_image[i, j, 0]:\r\n                # print(input_image[i,j,0])\r\n                mask[i, j, :] = 255.0\r\n            else:\r\n                mask[i, j, :] = 0.0  # input_image[i,j,:]\r\n    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))\r\n    mask = cv2.morphologyEx(np.array(mask), cv2.MORPH_OPEN, kernel, iterations=1)\r\n    return mask\r\n\r\n\r\ndef prepare_mask_and_masked_image(image, mask):\r\n    \"\"\"\r\n    Prepares a pair (image, mask) to be consumed by the Stable Diffusion pipeline. This means that those inputs will be\r\n    converted to ``torch.Tensor`` with shapes ``batch x channels x height x width`` where ``channels`` is ``3`` for the\r\n    ``image`` and ``1`` for the ``mask``.\r\n    The ``image`` will be converted to ``torch.float32`` and normalized to be in ``[-1, 1]``. The ``mask`` will be\r\n    binarized (``mask > 0.5``) and cast to ``torch.float32`` too.\r\n    Args:\r\n        image (Union[np.array, PIL.Image, torch.Tensor]): The image to inpaint.\r\n            It can be a ``PIL.Image``, or a ``height x width x 3`` ``np.array`` or a ``channels x height x width``\r\n            ``torch.Tensor`` or a ``batch x channels x height x width`` ``torch.Tensor``.\r\n        mask (_type_): The mask to apply to the image, i.e. regions to inpaint.\r\n            It can be a ``PIL.Image``, or a ``height x width`` ``np.array`` or a ``1 x height x width``\r\n            ``torch.Tensor`` or a ``batch x 1 x height x width`` ``torch.Tensor``.\r\n    Raises:\r\n        ValueError: ``torch.Tensor`` images should be in the ``[-1, 1]`` range. ValueError: ``torch.Tensor`` mask\r\n        should be in the ``[0, 1]`` range. ValueError: ``mask`` and ``image`` should have the same spatial dimensions.\r\n        TypeError: ``mask`` is a ``torch.Tensor`` but ``image`` is not\r\n            (ot the other way around).\r\n    Returns:\r\n        tuple[torch.Tensor]: The pair (mask, masked_image) as ``torch.Tensor`` with 4\r\n            dimensions: ``batch x channels x height x width``.\r\n    \"\"\"\r\n    if isinstance(image, torch.Tensor):\r\n        if not isinstance(mask, torch.Tensor):\r\n            raise TypeError(\r\n                f\"`image` is a torch.Tensor but `mask` (type: {type(mask)} is not\"\r\n            )\r\n\r\n        # Batch single image\r\n        if image.ndim == 3:\r\n            assert image.shape[0] == 3, (\r\n                \"Image outside a batch should be of shape (3, H, W)\"\r\n            )\r\n            image = image.unsqueeze(0)\r\n\r\n        # Batch and add channel dim for single mask\r\n        if mask.ndim == 2:\r\n            mask = mask.unsqueeze(0).unsqueeze(0)\r\n\r\n        # Batch single mask or add channel dim\r\n        if mask.ndim == 3:\r\n            # Single batched mask, no channel dim or single mask not batched but channel dim\r\n            if mask.shape[0] == 1:\r\n                mask = mask.unsqueeze(0)\r\n\r\n            # Batched masks no channel dim\r\n            else:\r\n                mask = mask.unsqueeze(1)\r\n\r\n        assert image.ndim == 4 and mask.ndim == 4, (\r\n            \"Image and Mask must have 4 dimensions\"\r\n        )\r\n        assert image.shape[-2:] == mask.shape[-2:], (\r\n            \"Image and Mask must have the same spatial dimensions\"\r\n        )\r\n        assert image.shape[0] == mask.shape[0], (\r\n            \"Image and Mask must have the same batch size\"\r\n        )\r\n\r\n        # Check image is in [-1, 1]\r\n        if image.min() < -1 or image.max() > 1:\r\n            raise ValueError(\"Image should be in [-1, 1] range\")\r\n\r\n        # Check mask is in [0, 1]\r\n        if mask.min() < 0 or mask.max() > 1:\r\n            raise ValueError(\"Mask should be in [0, 1] range\")\r\n\r\n        # Binarize mask\r\n        mask[mask < 0.5] = 0\r\n        mask[mask >= 0.5] = 1\r\n\r\n        # Image as float32\r\n        image = image.to(dtype=torch.float32)\r\n    elif isinstance(mask, torch.Tensor):\r\n        raise TypeError(\r\n            f\"`mask` is a torch.Tensor but `image` (type: {type(image)} is not\"\r\n        )\r\n    else:\r\n        # preprocess image\r\n        if isinstance(image, (Image.Image, np.ndarray)):\r\n            image = [image]\r\n\r\n        if isinstance(image, list) and isinstance(image[0], Image.Image):\r\n            image = [np.array(i.convert(\"RGB\"))[None, :] for i in image]\r\n            image = np.concatenate(image, axis=0)\r\n        elif isinstance(image, list) and isinstance(image[0], np.ndarray):\r\n            image = np.concatenate([i[None, :] for i in image], axis=0)\r\n\r\n        image = image.transpose(0, 3, 1, 2)\r\n        image = torch.from_numpy(image).to(dtype=torch.float32) / 127.5 - 1.0\r\n\r\n        # preprocess mask\r\n        if isinstance(mask, (Image.Image, np.ndarray)):\r\n            mask = [mask]\r\n\r\n        if isinstance(mask, list) and isinstance(mask[0], Image.Image):\r\n            mask = np.concatenate(\r\n                [np.array(m.convert(\"L\"))[None, None, :] for m in mask], axis=0\r\n            )\r\n            mask = mask.astype(np.float32) / 255.0\r\n        elif isinstance(mask, list) and isinstance(mask[0], np.ndarray):\r\n            mask = np.concatenate([m[None, None, :] for m in mask], axis=0)\r\n\r\n        mask[mask < 0.5] = 0\r\n        mask[mask >= 0.5] = 1\r\n        mask = torch.from_numpy(mask)\r\n\r\n    masked_image = image * (mask < 0.5)\r\n\r\n    return mask, masked_image\r\n\r\n\r\n# generate image\r\ngenerator = torch.manual_seed(859311133)  # 0\r\n\r\n\r\ndef path2L(img_path):\r\n    raw_image = cv2.imread(img_path)\r\n    raw_image = cv2.cvtColor(raw_image, cv2.COLOR_BGR2LAB)\r\n    raw_image_input = cv2.merge(\r\n        [raw_image[:, :, 0], raw_image[:, :, 0], raw_image[:, :, 0]]\r\n    )\r\n    return raw_image_input\r\n\r\n\r\ndef is_gray_scale(img, threshold=10):\r\n    img = Image.fromarray(img)\r\n    if len(img.getbands()) == 1:\r\n        return True\r\n    img1 = np.asarray(img.getchannel(channel=0), dtype=np.int16)\r\n    img2 = np.asarray(img.getchannel(channel=1), dtype=np.int16)\r\n    img3 = np.asarray(img.getchannel(channel=2), dtype=np.int16)\r\n    diff1 = (img1 - img2).var()\r\n    diff2 = (img2 - img3).var()\r\n    diff3 = (img3 - img1).var()\r\n    diff_sum = (diff1 + diff2 + diff3) / 3.0\r\n    if diff_sum <= threshold:\r\n        return True\r\n    else:\r\n        return False\r\n\r\n\r\ndef randn_tensor(\r\n    shape,\r\n    generator=None,\r\n    device=None,\r\n    dtype=None,\r\n    layout=None,\r\n):\r\n    \"\"\"A helper function to create random tensors on the desired `device` with the desired `dtype`. When\r\n    passing a list of generators, you can seed each batch size individually. If CPU generators are passed, the tensor\r\n    is always created on the CPU.\r\n    \"\"\"\r\n    # device on which tensor is created defaults to device\r\n    rand_device = device\r\n    batch_size = shape[0]\r\n\r\n    layout = layout or torch.strided\r\n    device = device or torch.device(\"cpu\")\r\n\r\n    if generator is not None:\r\n        gen_device_type = (\r\n            generator.device.type\r\n            if not isinstance(generator, list)\r\n            else generator[0].device.type\r\n        )\r\n        if gen_device_type != device.type and gen_device_type == \"cpu\":\r\n            rand_device = \"cpu\"\r\n            if device != \"mps\":\r\n                print(\r\n                    \"The passed generator was created on 'cpu' even though a tensor on {device} was expected.\"\r\n                )\r\n                # logger.info(\r\n                #     f\"The passed generator was created on 'cpu' even though a tensor on {device} was expected.\"\r\n                #     f\" Tensors will be created on 'cpu' and then moved to {device}. Note that one can probably\"\r\n                #     f\" slighly speed up this function by passing a generator that was created on the {device} device.\"\r\n                # )\r\n        elif gen_device_type != device.type and gen_device_type == \"cuda\":\r\n            raise ValueError(\r\n                f\"Cannot generate a {device} tensor from a generator of type {gen_device_type}.\"\r\n            )\r\n\r\n    # make sure generator list of length 1 is treated like a non-list\r\n    if isinstance(generator, list) and len(generator) == 1:\r\n        generator = generator[0]\r\n\r\n    if isinstance(generator, list):\r\n        shape = (1,) + shape[1:]\r\n        latents = [\r\n            torch.randn(\r\n                shape,\r\n                generator=generator[i],\r\n                device=rand_device,\r\n                dtype=dtype,\r\n                layout=layout,\r\n            )\r\n            for i in range(batch_size)\r\n        ]\r\n        latents = torch.cat(latents, dim=0).to(device)\r\n    else:\r\n        latents = torch.randn(\r\n            shape, generator=generator, device=rand_device, dtype=dtype, layout=layout\r\n        ).to(device)\r\n\r\n    return latents\r\n\r\n\r\ndef add_noise(\r\n    original_samples: torch.FloatTensor,\r\n    noise: torch.FloatTensor,\r\n    timesteps: torch.IntTensor,\r\n) -> torch.FloatTensor:\r\n    betas = torch.linspace(0.00085, 0.0120, 1000, dtype=torch.float32)\r\n    alphas = 1.0 - betas\r\n    alphas_cumprod = torch.cumprod(alphas, dim=0)\r\n    alphas_cumprod = alphas_cumprod.to(\r\n        device=original_samples.device, dtype=original_samples.dtype\r\n    )\r\n    timesteps = timesteps.to(original_samples.device)\r\n\r\n    sqrt_alpha_prod = alphas_cumprod[timesteps] ** 0.5\r\n    sqrt_alpha_prod = sqrt_alpha_prod.flatten()\r\n    while len(sqrt_alpha_prod.shape) < len(original_samples.shape):\r\n        sqrt_alpha_prod = sqrt_alpha_prod.unsqueeze(-1)\r\n\r\n    sqrt_one_minus_alpha_prod = (1 - alphas_cumprod[timesteps]) ** 0.5\r\n    sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.flatten()\r\n    while len(sqrt_one_minus_alpha_prod.shape) < len(original_samples.shape):\r\n        sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.unsqueeze(-1)\r\n\r\n    noisy_samples = (\r\n        sqrt_alpha_prod * original_samples + sqrt_one_minus_alpha_prod * noise\r\n    )\r\n\r\n    return noisy_samples\r\n\r\n\r\ndef set_timesteps(num_inference_steps: int, timestep_spacing=\"leading\", device=None):\r\n    \"\"\"\r\n    Sets the discrete timesteps used for the diffusion chain. Supporting function to be run before inference.\r\n\r\n    Args:\r\n        num_inference_steps (`int`):\r\n            the number of diffusion steps used when generating samples with a pre-trained model.\r\n    \"\"\"\r\n    num_train_timesteps = 1000\r\n    if num_inference_steps > num_train_timesteps:\r\n        raise ValueError(\r\n            f\"`num_inference_steps`: {num_inference_steps} cannot be larger than `self.config.train_timesteps`:\"\r\n            f\" {num_train_timesteps} as the unet model trained with this scheduler can only handle\"\r\n            f\" maximal {num_train_timesteps} timesteps.\"\r\n        )\r\n\r\n    num_inference_steps = num_inference_steps\r\n    # \"linspace\", \"leading\", \"trailing\" corresponds to annotation of Table 2. of https://arxiv.org/abs/2305.08891\r\n    if timestep_spacing == \"linspace\":\r\n        timesteps = (\r\n            np.linspace(0, num_train_timesteps - 1, num_inference_steps)\r\n            .round()[::-1]\r\n            .copy()\r\n            .astype(np.int64)\r\n        )\r\n    elif timestep_spacing == \"leading\":\r\n        step_ratio = num_train_timesteps // num_inference_steps\r\n        # creates integer timesteps by multiplying by ratio\r\n        # casting to int to avoid issues when num_inference_step is power of 3\r\n        timesteps = (\r\n            (np.arange(0, num_inference_steps) * step_ratio)\r\n            .round()[::-1]\r\n            .copy()\r\n            .astype(np.int64)\r\n        )\r\n        # timesteps += steps_offset\r\n    elif timestep_spacing == \"trailing\":\r\n        step_ratio = num_train_timesteps / num_inference_steps\r\n        # creates integer timesteps by multiplying by ratio\r\n        # casting to int to avoid issues when num_inference_step is power of 3\r\n        timesteps = np.round(np.arange(num_train_timesteps, 0, -step_ratio)).astype(\r\n            np.int64\r\n        )\r\n        timesteps -= 1\r\n    else:\r\n        raise ValueError(\r\n            f\"{timestep_spacing} is not supported. Please make sure to choose one of 'leading' or 'trailing'.\"\r\n        )\r\n\r\n    timesteps = torch.from_numpy(timesteps).to(device)\r\n    return timesteps\r\n\r\n\r\ndef get_timesteps(num_inference_steps, timesteps_set, strength, device):\r\n    # get the original timestep using init_timestep\r\n    init_timestep = min(int(num_inference_steps * strength), num_inference_steps)\r\n\r\n    t_start = max(num_inference_steps - init_timestep, 0)\r\n    timesteps = timesteps_set[t_start * 1 :]\r\n\r\n    return timesteps, num_inference_steps - t_start\r\n\r\n\r\ndef get_noised_image_latents(img, W, H, ddim_steps, strength, seed, device):\r\n    img1 = [cv2.resize(img, (W, H))]\r\n    img1 = np.concatenate([i[None, :] for i in img1], axis=0)\r\n    img1 = img1.transpose(0, 3, 1, 2)\r\n    img1 = torch.from_numpy(img1).to(dtype=torch.float32) / 127.5 - 1.0\r\n\r\n    image_latents = model.get_first_stage_encoding(\r\n        model.encode_first_stage(img1.cuda())\r\n    ).detach()\r\n    shape = image_latents.shape\r\n    generator = torch.manual_seed(seed)\r\n\r\n    noise = randn_tensor(shape, generator=generator, device=device, dtype=torch.float32)\r\n\r\n    timesteps_set = set_timesteps(\r\n        ddim_steps, timestep_spacing=\"linspace\", device=device\r\n    )\r\n    timesteps, num_inference_steps = get_timesteps(\r\n        ddim_steps, timesteps_set, strength, device\r\n    )\r\n    latent_timestep = timesteps[1].repeat(1 * 1)\r\n\r\n    init_latents = add_noise(image_latents, noise, torch.tensor(latent_timestep))\r\n    for j in range(0, 1000, 100):\r\n        x_samples = model.decode_first_stage(\r\n            add_noise(image_latents, noise, torch.tensor(j))\r\n        )\r\n        init_image = (\r\n            (einops.rearrange(x_samples, \"b c h w -> b h w c\") * 127.5 + 127.5)\r\n            .cpu()\r\n            .numpy()\r\n            .clip(0, 255)\r\n            .astype(np.uint8)\r\n        )\r\n\r\n        cv2.imwrite(\r\n            \"./initlatents1/\" + str(j) + \"init_image.png\",\r\n            cv2.cvtColor(init_image[0], cv2.COLOR_RGB2BGR),\r\n        )\r\n    return init_latents\r\n\r\n\r\ndef process(\r\n    using_deformable_vae,\r\n    change_according_to_strokes,\r\n    iterative_editing,\r\n    input_image,\r\n    hint_image,\r\n    prompt,\r\n    a_prompt,\r\n    n_prompt,\r\n    num_samples,\r\n    image_resolution,\r\n    ddim_steps,\r\n    guess_mode,\r\n    strength,\r\n    scale,\r\n    sag_scale,\r\n    SAG_influence_step,\r\n    seed,\r\n    eta,\r\n):\r\n    torch.cuda.empty_cache()\r\n    with torch.no_grad():\r\n        ref_flag = True\r\n        input_image_ori = input_image\r\n        if is_gray_scale(input_image):\r\n            print(\"It is a greyscale image.\")\r\n            # mask=get_mask(input_image,hint_image)\r\n        else:\r\n            print(\"It is a color image.\")\r\n            input_image_ori = input_image\r\n            input_image = cv2.cvtColor(input_image, cv2.COLOR_RGB2LAB)[:, :, 0]\r\n            input_image = cv2.merge([input_image, input_image, input_image])\r\n        mask = get_mask(input_image_ori, hint_image)\r\n        cv2.imwrite(\"./logs/gradio_mask1.png\", mask)\r\n\r\n        if iterative_editing:\r\n            mask = 255 - mask\r\n            if change_according_to_strokes:\r\n                kernel = np.ones((15, 15), np.uint8)\r\n                mask = cv2.morphologyEx(mask, cv2.MORPH_GRADIENT, kernel)\r\n                hint_image = mask / 255.0 * hint_image + (1 - mask / 255.0) * hint_image\r\n            else:\r\n                hint_image = (\r\n                    mask / 255.0 * input_image + (1 - mask / 255.0) * input_image_ori\r\n                )\r\n        else:\r\n            hint_image = mask / 255.0 * input_image + (1 - mask / 255.0) * hint_image\r\n        hint_image = hint_image.astype(np.uint8)\r\n        if len(prompt) == 0:\r\n            image = Image.fromarray(input_image)\r\n            image = vis_processors[\"eval\"](image).unsqueeze(0).to(device)\r\n            prompt = BLIP_model.generate({\"image\": image})[0]\r\n            if (\r\n                \"a black and white photo of\" in prompt\r\n                or \"black and white photograph of\" in prompt\r\n            ):\r\n                prompt = prompt.replace(prompt[: prompt.find(\"of\") + 3], \"\")\r\n        print(prompt)\r\n        H_ori, W_ori, C_ori = input_image.shape\r\n        img = resize_image(input_image, image_resolution)\r\n        mask = resize_image(mask, image_resolution)\r\n        hint_image = resize_image(hint_image, image_resolution)\r\n        mask, masked_image = prepare_mask_and_masked_image(\r\n            Image.fromarray(hint_image), Image.fromarray(mask)\r\n        )\r\n        mask, masked_image_latents = encode_mask(mask, masked_image)\r\n        H, W, C = img.shape\r\n\r\n        # if ref_image is None:\r\n        ref_image = np.array([[[0] * C] * W] * H).astype(np.float32)\r\n        # print(ref_image.shape)\r\n        # ref_flag=False\r\n        ref_image = resize_image(ref_image, image_resolution)\r\n\r\n        # cv2.imwrite(\"./logs/exemplar_image.png\",cv2.cvtColor(ref_image,cv2.COLOR_RGB2BGR))\r\n\r\n        # ddim_steps=1\r\n        control = torch.from_numpy(img.copy()).float().cuda() / 255.0\r\n        control = torch.stack([control for _ in range(num_samples)], dim=0)\r\n        control = einops.rearrange(control, \"b h w c -> b c h w\").clone()\r\n\r\n        if seed == -1:\r\n            seed = random.randint(0, 65535)\r\n        seed_everything(seed)\r\n\r\n        ref_image = cv2.resize(ref_image, (W, H))\r\n\r\n        ref_image = torch.from_numpy(ref_image).cuda().unsqueeze(0)\r\n\r\n        init_latents = None\r\n\r\n        if config.save_memory:\r\n            model.low_vram_shift(is_diffusing=False)\r\n\r\n        print(\"no reference images, using Frozen encoder\")\r\n        cond = {\r\n            \"c_concat\": [control],\r\n            \"c_crossattn\": [\r\n                model.get_learned_conditioning([prompt + \", \" + a_prompt] * num_samples)\r\n            ],\r\n        }\r\n        un_cond = {\r\n            \"c_concat\": None if guess_mode else [control],\r\n            \"c_crossattn\": [model.get_learned_conditioning([n_prompt] * num_samples)],\r\n        }\r\n        shape = (4, H // 8, W // 8)\r\n\r\n        if config.save_memory:\r\n            model.low_vram_shift(is_diffusing=True)\r\n        noise = randn_tensor(\r\n            shape, generator=generator, device=device, dtype=torch.float32\r\n        )\r\n        model.control_scales = (\r\n            [strength * (0.825 ** float(12 - i)) for i in range(13)]\r\n            if guess_mode\r\n            else ([strength] * 13)\r\n        )  # Magic number. IDK why. Perhaps because 0.825**12<0.01 but 0.826**12>0.01\r\n        samples, intermediates = ddim_sampler.sample(\r\n            model,\r\n            ddim_steps,\r\n            num_samples,\r\n            shape,\r\n            cond,\r\n            mask=mask,\r\n            masked_image_latents=masked_image_latents,\r\n            verbose=False,\r\n            eta=eta,\r\n            #  x_T=image_latents,\r\n            x_T=init_latents,\r\n            unconditional_guidance_scale=scale,\r\n            sag_scale=sag_scale,\r\n            SAG_influence_step=SAG_influence_step,\r\n            noise=noise,\r\n            unconditional_conditioning=un_cond,\r\n        )\r\n\r\n        if config.save_memory:\r\n            model.low_vram_shift(is_diffusing=False)\r\n\r\n        if not using_deformable_vae:\r\n            x_samples = model.decode_first_stage(samples)\r\n        else:\r\n            samples = model.decode_first_stage_before_vae(samples)\r\n            gray_content_z = vae_model.get_gray_content_z(\r\n                torch.from_numpy(img.copy()).float().cuda() / 255.0\r\n            )\r\n            # print(gray_content_z.shape)\r\n            x_samples = vae_model.decode(samples, gray_content_z)\r\n\r\n        x_samples = (\r\n            (einops.rearrange(x_samples, \"b c h w -> b h w c\") * 127.5 + 127.5)\r\n            .cpu()\r\n            .numpy()\r\n            .clip(0, 255)\r\n            .astype(np.uint8)\r\n        )\r\n\r\n        # single image replace L channel\r\n        results_ori = [x_samples[i] for i in range(num_samples)]\r\n        results_ori = [\r\n            cv2.resize(i, (W_ori, H_ori), interpolation=cv2.INTER_LANCZOS4)\r\n            for i in results_ori\r\n        ]\r\n\r\n        cv2.imwrite(\r\n            \"./logs/result_ori.png\", cv2.cvtColor(results_ori[0], cv2.COLOR_RGB2BGR)\r\n        )\r\n\r\n        results_tmp = [\r\n            cv2.cvtColor(np.array(i), cv2.COLOR_RGB2LAB) for i in results_ori\r\n        ]\r\n        results = [\r\n            cv2.merge([input_image[:, :, 0], tmp[:, :, 1], tmp[:, :, 2]])\r\n            for tmp in results_tmp\r\n        ]\r\n        results_mergeL = [\r\n            cv2.cvtColor(np.asarray(i), cv2.COLOR_LAB2RGB) for i in results\r\n        ]  # cv2.COLOR_LAB2BGR)\r\n        cv2.imwrite(\r\n            \"./logs/output.png\", cv2.cvtColor(results_mergeL[0], cv2.COLOR_RGB2BGR)\r\n        )\r\n    return results_mergeL\r\n\r\n\r\ndef get_grayscale_img(img, progress=gr.Progress(track_tqdm=True)):\r\n    torch.cuda.empty_cache()\r\n    for j in tqdm.tqdm(range(1), desc=\"Uploading input...\"):\r\n        return img, \"Uploading input image done.\"\r\n\r\n\r\nblock = gr.Blocks().queue()\r\nwith block:\r\n    with gr.Row():\r\n        gr.Markdown(\r\n            \"## Control-Color\"\r\n        )  # (\"## Color-Anything\")#Control Stable Diffusion with L channel\r\n    with gr.Row():\r\n        with gr.Column():\r\n            # input_image = gr.Image(source='upload', type=\"numpy\")\r\n            grayscale_img = gr.Image(visible=False, type=\"numpy\")\r\n            input_image = gr.Image(\r\n                source=\"upload\", tool=\"color-sketch\", interactive=True\r\n            )\r\n            Grayscale_button = gr.Button(value=\"Upload input image\")\r\n            text_out = gr.Textbox(\r\n                value=\"Please upload input image first, then draw the strokes or input text prompts or give reference images as you wish.\"\r\n            )\r\n            prompt = gr.Textbox(label=\"Prompt\")\r\n            change_according_to_strokes = gr.Checkbox(\r\n                label=\"Change according to strokes' color\", value=True\r\n            )\r\n            iterative_editing = gr.Checkbox(\r\n                label=\"Only change the strokes' area\", value=False\r\n            )\r\n            using_deformable_vae = gr.Checkbox(\r\n                label=\"Using deformable vae. (Less color overflow)\", value=False\r\n            )\r\n            # with gr.Accordion(\"Input Reference\", open=False):\r\n            #     ref_image = gr.Image(source='upload', type=\"numpy\")\r\n            run_button = gr.Button(\r\n                label=\"Upload prompts/strokes (optional) and Run\",\r\n                value=\"Upload prompts/strokes (optional) and Run\",\r\n            )\r\n            with gr.Accordion(\"Advanced options\", open=False):\r\n                num_samples = gr.Slider(\r\n                    label=\"Images\", minimum=1, maximum=12, value=1, step=1\r\n                )\r\n                image_resolution = gr.Slider(\r\n                    label=\"Image Resolution\",\r\n                    minimum=256,\r\n                    maximum=768,\r\n                    value=512,\r\n                    step=64,\r\n                )\r\n                strength = gr.Slider(\r\n                    label=\"Control Strength\",\r\n                    minimum=0.0,\r\n                    maximum=2.0,\r\n                    value=1.0,\r\n                    step=0.01,\r\n                )\r\n                guess_mode = gr.Checkbox(label=\"Guess Mode\", value=False)\r\n                # detect_resolution = gr.Slider(label=\"Depth Resolution\", minimum=128, maximum=1024, value=384, step=1)\r\n                ddim_steps = gr.Slider(\r\n                    label=\"Steps\", minimum=1, maximum=100, value=20, step=1\r\n                )\r\n                scale = gr.Slider(\r\n                    label=\"Guidance Scale\",\r\n                    minimum=0.1,\r\n                    maximum=30.0,\r\n                    value=7.0,\r\n                    step=0.1,\r\n                )  # value=9.0\r\n                sag_scale = gr.Slider(\r\n                    label=\"SAG Scale\", minimum=0.0, maximum=1.0, value=0.05, step=0.01\r\n                )  # 0.08\r\n                SAG_influence_step = gr.Slider(\r\n                    label=\"1000-SAG influence step\",\r\n                    minimum=0,\r\n                    maximum=900,\r\n                    value=600,\r\n                    step=50,\r\n                )\r\n                seed = gr.Slider(\r\n                    label=\"Seed\", minimum=-1, maximum=2147483647, step=1, randomize=True\r\n                )  # 94433242802\r\n                eta = gr.Number(label=\"eta (DDIM)\", value=0.0)\r\n                a_prompt = gr.Textbox(\r\n                    label=\"Added Prompt\", value=\"best quality, detailed, real\"\r\n                )  # extremely detailed\r\n                n_prompt = gr.Textbox(\r\n                    label=\"Negative Prompt\",\r\n                    value=\"a black and white photo, longbody, lowres, bad anatomy, bad hands, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality\",\r\n                )\r\n        with gr.Column():\r\n            result_gallery = gr.Gallery(\r\n                label=\"Output\", show_label=False, elem_id=\"gallery\"\r\n            ).style(grid=2, height=\"auto\")\r\n            # grayscale_img = gr.Image(interactive=False,visible=False)\r\n\r\n    Grayscale_button.click(\r\n        fn=get_grayscale_img, inputs=input_image, outputs=[grayscale_img, text_out]\r\n    )\r\n    ips = [\r\n        using_deformable_vae,\r\n        change_according_to_strokes,\r\n        iterative_editing,\r\n        grayscale_img,\r\n        input_image,\r\n        prompt,\r\n        a_prompt,\r\n        n_prompt,\r\n        num_samples,\r\n        image_resolution,\r\n        ddim_steps,\r\n        guess_mode,\r\n        strength,\r\n        scale,\r\n        sag_scale,\r\n        SAG_influence_step,\r\n        seed,\r\n        eta,\r\n    ]\r\n    run_button.click(fn=process, inputs=ips, outputs=[result_gallery])\r\n\r\n\r\nblock.launch(server_name=\"0.0.0.0\", share=True)\r\n", "modifiedCode": "import os\r\nimport random\r\n\r\nimport cv2\r\nimport einops\r\nimport gradio as gr\r\nimport numpy as np\r\nimport torch\r\nimport tqdm\r\nfrom lavis.models import load_model_and_preprocess\r\nfrom PIL import Image\r\nfrom pytorch_lightning import seed_everything\r\n\r\nimport config\r\nfrom annotator.util import resize_image\r\nfrom cldm.ddim_haced_sag_step import DDIMSampler\r\nfrom cldm.model import create_model, load_state_dict\r\nfrom ldm.models.autoencoder_train import AutoencoderKL\r\nfrom share import *\r\n\r\n# write images to logs folder\r\nos.makedirs(\"logs\", exist_ok=True)\r\n\r\nckpt_path = \"./pretrained_models/main_model.ckpt\"\r\n\r\nmodel = create_model(\"./models/cldm_v15_inpainting_infer1.yaml\").cpu()\r\nmodel.load_state_dict(load_state_dict(ckpt_path, location=\"cuda\"), strict=False)\r\nmodel = model.cuda()\r\n\r\nddim_sampler = DDIMSampler(model)\r\n\r\n\r\ndevice = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\r\nBLIP_model, vis_processors, _ = load_model_and_preprocess(\r\n    name=\"blip_caption\", model_type=\"base_coco\", is_eval=True, device=device\r\n)\r\n\r\nvae_model_ckpt_path = \"./pretrained_models/content-guided_deformable_vae.ckpt\"\r\n\r\n\r\ndef load_vae():\r\n    init_config = {\r\n        \"embed_dim\": 4,\r\n        \"monitor\": \"val/rec_loss\",\r\n        \"ddconfig\": {\r\n            \"double_z\": True,\r\n            \"z_channels\": 4,\r\n            \"resolution\": 256,\r\n            \"in_channels\": 3,\r\n            \"out_ch\": 3,\r\n            \"ch\": 128,\r\n            \"ch_mult\": [1, 2, 4, 4],\r\n            \"num_res_blocks\": 2,\r\n            \"attn_resolutions\": [],\r\n            \"dropout\": 0.0,\r\n        },\r\n        \"lossconfig\": {\r\n            \"target\": \"ldm.modules.losses.LPIPSWithDiscriminator\",\r\n            \"params\": {\r\n                \"disc_start\": 501,\r\n                \"kl_weight\": 0,\r\n                \"disc_weight\": 0.025,\r\n                \"disc_factor\": 1.0,\r\n            },\r\n        },\r\n    }\r\n    vae = AutoencoderKL(**init_config)\r\n    vae.load_state_dict(load_state_dict(vae_model_ckpt_path, location=\"cuda\"))\r\n    vae = vae.cuda()\r\n    return vae\r\n\r\n\r\nvae_model = load_vae()\r\n\r\n\r\ndef encode_mask(mask, masked_image):\r\n    mask = torch.nn.functional.interpolate(\r\n        mask, size=(mask.shape[2] // 8, mask.shape[3] // 8)\r\n    )\r\n    # mask=torch.cat([mask] * 2) #if do_classifier_free_guidance else mask\r\n    mask = mask.to(device=\"cuda\")\r\n    # do_classifier_free_guidance=False\r\n    masked_image_latents = model.get_first_stage_encoding(\r\n        model.encode_first_stage(masked_image.cuda())\r\n    ).detach()\r\n    return mask, masked_image_latents\r\n\r\n\r\ndef get_mask(input_image, hint_image):\r\n    mask = input_image.copy()\r\n    H, W, C = input_image.shape\r\n    for i in range(H):\r\n        for j in range(W):\r\n            if input_image[i, j, 0] == hint_image[i, j, 0]:\r\n                # print(input_image[i,j,0])\r\n                mask[i, j, :] = 255.0\r\n            else:\r\n                mask[i, j, :] = 0.0  # input_image[i,j,:]\r\n    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))\r\n    mask = cv2.morphologyEx(np.array(mask), cv2.MORPH_OPEN, kernel, iterations=1)\r\n    return mask\r\n\r\n\r\ndef prepare_mask_and_masked_image(image, mask):\r\n    \"\"\"\r\n    Prepares a pair (image, mask) to be consumed by the Stable Diffusion pipeline. This means that those inputs will be\r\n    converted to ``torch.Tensor`` with shapes ``batch x channels x height x width`` where ``channels`` is ``3`` for the\r\n    ``image`` and ``1`` for the ``mask``.\r\n    The ``image`` will be converted to ``torch.float32`` and normalized to be in ``[-1, 1]``. The ``mask`` will be\r\n    binarized (``mask > 0.5``) and cast to ``torch.float32`` too.\r\n    Args:\r\n        image (Union[np.array, PIL.Image, torch.Tensor]): The image to inpaint.\r\n            It can be a ``PIL.Image``, or a ``height x width x 3`` ``np.array`` or a ``channels x height x width``\r\n            ``torch.Tensor`` or a ``batch x channels x height x width`` ``torch.Tensor``.\r\n        mask (_type_): The mask to apply to the image, i.e. regions to inpaint.\r\n            It can be a ``PIL.Image``, or a ``height x width`` ``np.array`` or a ``1 x height x width``\r\n            ``torch.Tensor`` or a ``batch x 1 x height x width`` ``torch.Tensor``.\r\n    Raises:\r\n        ValueError: ``torch.Tensor`` images should be in the ``[-1, 1]`` range. ValueError: ``torch.Tensor`` mask\r\n        should be in the ``[0, 1]`` range. ValueError: ``mask`` and ``image`` should have the same spatial dimensions.\r\n        TypeError: ``mask`` is a ``torch.Tensor`` but ``image`` is not\r\n            (ot the other way around).\r\n    Returns:\r\n        tuple[torch.Tensor]: The pair (mask, masked_image) as ``torch.Tensor`` with 4\r\n            dimensions: ``batch x channels x height x width``.\r\n    \"\"\"\r\n    if isinstance(image, torch.Tensor):\r\n        if not isinstance(mask, torch.Tensor):\r\n            raise TypeError(\r\n                f\"`image` is a torch.Tensor but `mask` (type: {type(mask)} is not\"\r\n            )\r\n\r\n        # Batch single image\r\n        if image.ndim == 3:\r\n            assert image.shape[0] == 3, (\r\n                \"Image outside a batch should be of shape (3, H, W)\"\r\n            )\r\n            image = image.unsqueeze(0)\r\n\r\n        # Batch and add channel dim for single mask\r\n        if mask.ndim == 2:\r\n            mask = mask.unsqueeze(0).unsqueeze(0)\r\n\r\n        # Batch single mask or add channel dim\r\n        if mask.ndim == 3:\r\n            # Single batched mask, no channel dim or single mask not batched but channel dim\r\n            if mask.shape[0] == 1:\r\n                mask = mask.unsqueeze(0)\r\n\r\n            # Batched masks no channel dim\r\n            else:\r\n                mask = mask.unsqueeze(1)\r\n\r\n        assert image.ndim == 4 and mask.ndim == 4, (\r\n            \"Image and Mask must have 4 dimensions\"\r\n        )\r\n        assert image.shape[-2:] == mask.shape[-2:], (\r\n            \"Image and Mask must have the same spatial dimensions\"\r\n        )\r\n        assert image.shape[0] == mask.shape[0], (\r\n            \"Image and Mask must have the same batch size\"\r\n        )\r\n\r\n        # Check image is in [-1, 1]\r\n        if image.min() < -1 or image.max() > 1:\r\n            raise ValueError(\"Image should be in [-1, 1] range\")\r\n\r\n        # Check mask is in [0, 1]\r\n        if mask.min() < 0 or mask.max() > 1:\r\n            raise ValueError(\"Mask should be in [0, 1] range\")\r\n\r\n        # Binarize mask\r\n        mask[mask < 0.5] = 0\r\n        mask[mask >= 0.5] = 1\r\n\r\n        # Image as float32\r\n        image = image.to(dtype=torch.float32)\r\n    elif isinstance(mask, torch.Tensor):\r\n        raise TypeError(\r\n            f\"`mask` is a torch.Tensor but `image` (type: {type(image)} is not\"\r\n        )\r\n    else:\r\n        # preprocess image\r\n        if isinstance(image, (Image.Image, np.ndarray)):\r\n            image = [image]\r\n\r\n        if isinstance(image, list) and isinstance(image[0], Image.Image):\r\n            image = [np.array(i.convert(\"RGB\"))[None, :] for i in image]\r\n            image = np.concatenate(image, axis=0)\r\n        elif isinstance(image, list) and isinstance(image[0], np.ndarray):\r\n            image = np.concatenate([i[None, :] for i in image], axis=0)\r\n\r\n        image = image.transpose(0, 3, 1, 2)\r\n        image = torch.from_numpy(image).to(dtype=torch.float32) / 127.5 - 1.0\r\n\r\n        # preprocess mask\r\n        if isinstance(mask, (Image.Image, np.ndarray)):\r\n            mask = [mask]\r\n\r\n        if isinstance(mask, list) and isinstance(mask[0], Image.Image):\r\n            mask = np.concatenate(\r\n                [np.array(m.convert(\"L\"))[None, None, :] for m in mask], axis=0\r\n            )\r\n            mask = mask.astype(np.float32) / 255.0\r\n        elif isinstance(mask, list) and isinstance(mask[0], np.ndarray):\r\n            mask = np.concatenate([m[None, None, :] for m in mask], axis=0)\r\n\r\n        mask[mask < 0.5] = 0\r\n        mask[mask >= 0.5] = 1\r\n        mask = torch.from_numpy(mask)\r\n\r\n    masked_image = image * (mask < 0.5)\r\n\r\n    return mask, masked_image\r\n\r\n\r\n# generate image\r\ngenerator = torch.manual_seed(859311133)  # 0\r\n\r\n\r\ndef path2L(img_path):\r\n    raw_image = cv2.imread(img_path)\r\n    raw_image = cv2.cvtColor(raw_image, cv2.COLOR_BGR2LAB)\r\n    raw_image_input = cv2.merge(\r\n        [raw_image[:, :, 0], raw_image[:, :, 0], raw_image[:, :, 0]]\r\n    )\r\n    return raw_image_input\r\n\r\n\r\ndef is_gray_scale(img, threshold=10):\r\n    img = Image.fromarray(img)\r\n    if len(img.getbands()) == 1:\r\n        return True\r\n    img1 = np.asarray(img.getchannel(channel=0), dtype=np.int16)\r\n    img2 = np.asarray(img.getchannel(channel=1), dtype=np.int16)\r\n    img3 = np.asarray(img.getchannel(channel=2), dtype=np.int16)\r\n    diff1 = (img1 - img2).var()\r\n    diff2 = (img2 - img3).var()\r\n    diff3 = (img3 - img1).var()\r\n    diff_sum = (diff1 + diff2 + diff3) / 3.0\r\n    if diff_sum <= threshold:\r\n        return True\r\n    else:\r\n        return False\r\n\r\n\r\ndef randn_tensor(\r\n    shape,\r\n    generator=None,\r\n    device=None,\r\n    dtype=None,\r\n    layout=None,\r\n):\r\n    \"\"\"A helper function to create random tensors on the desired `device` with the desired `dtype`. When\r\n    passing a list of generators, you can seed each batch size individually. If CPU generators are passed, the tensor\r\n    is always created on the CPU.\r\n    \"\"\"\r\n    # device on which tensor is created defaults to device\r\n    rand_device = device\r\n    batch_size = shape[0]\r\n\r\n    layout = layout or torch.strided\r\n    device = device or torch.device(\"cpu\")\r\n\r\n    if generator is not None:\r\n        gen_device_type = (\r\n            generator.device.type\r\n            if not isinstance(generator, list)\r\n            else generator[0].device.type\r\n        )\r\n        if gen_device_type != device.type and gen_device_type == \"cpu\":\r\n            rand_device = \"cpu\"\r\n            if device != \"mps\":\r\n                print(\r\n                    \"The passed generator was created on 'cpu' even though a tensor on {device} was expected.\"\r\n                )\r\n                # logger.info(\r\n                #     f\"The passed generator was created on 'cpu' even though a tensor on {device} was expected.\"\r\n                #     f\" Tensors will be created on 'cpu' and then moved to {device}. Note that one can probably\"\r\n                #     f\" slighly speed up this function by passing a generator that was created on the {device} device.\"\r\n                # )\r\n        elif gen_device_type != device.type and gen_device_type == \"cuda\":\r\n            raise ValueError(\r\n                f\"Cannot generate a {device} tensor from a generator of type {gen_device_type}.\"\r\n            )\r\n\r\n    # make sure generator list of length 1 is treated like a non-list\r\n    if isinstance(generator, list) and len(generator) == 1:\r\n        generator = generator[0]\r\n\r\n    if isinstance(generator, list):\r\n        shape = (1,) + shape[1:]\r\n        latents = [\r\n            torch.randn(\r\n                shape,\r\n                generator=generator[i],\r\n                device=rand_device,\r\n                dtype=dtype,\r\n                layout=layout,\r\n            )\r\n            for i in range(batch_size)\r\n        ]\r\n        latents = torch.cat(latents, dim=0).to(device)\r\n    else:\r\n        latents = torch.randn(\r\n            shape, generator=generator, device=rand_device, dtype=dtype, layout=layout\r\n        ).to(device)\r\n\r\n    return latents\r\n\r\n\r\ndef add_noise(\r\n    original_samples: torch.FloatTensor,\r\n    noise: torch.FloatTensor,\r\n    timesteps: torch.IntTensor,\r\n) -> torch.FloatTensor:\r\n    betas = torch.linspace(0.00085, 0.0120, 1000, dtype=torch.float32)\r\n    alphas = 1.0 - betas\r\n    alphas_cumprod = torch.cumprod(alphas, dim=0)\r\n    alphas_cumprod = alphas_cumprod.to(\r\n        device=original_samples.device, dtype=original_samples.dtype\r\n    )\r\n    timesteps = timesteps.to(original_samples.device)\r\n\r\n    sqrt_alpha_prod = alphas_cumprod[timesteps] ** 0.5\r\n    sqrt_alpha_prod = sqrt_alpha_prod.flatten()\r\n    while len(sqrt_alpha_prod.shape) < len(original_samples.shape):\r\n        sqrt_alpha_prod = sqrt_alpha_prod.unsqueeze(-1)\r\n\r\n    sqrt_one_minus_alpha_prod = (1 - alphas_cumprod[timesteps]) ** 0.5\r\n    sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.flatten()\r\n    while len(sqrt_one_minus_alpha_prod.shape) < len(original_samples.shape):\r\n        sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.unsqueeze(-1)\r\n\r\n    noisy_samples = (\r\n        sqrt_alpha_prod * original_samples + sqrt_one_minus_alpha_prod * noise\r\n    )\r\n\r\n    return noisy_samples\r\n\r\n\r\ndef set_timesteps(num_inference_steps: int, timestep_spacing=\"leading\", device=None):\r\n    \"\"\"\r\n    Sets the discrete timesteps used for the diffusion chain. Supporting function to be run before inference.\r\n\r\n    Args:\r\n        num_inference_steps (`int`):\r\n            the number of diffusion steps used when generating samples with a pre-trained model.\r\n    \"\"\"\r\n    num_train_timesteps = 1000\r\n    if num_inference_steps > num_train_timesteps:\r\n        raise ValueError(\r\n            f\"`num_inference_steps`: {num_inference_steps} cannot be larger than `self.config.train_timesteps`:\"\r\n            f\" {num_train_timesteps} as the unet model trained with this scheduler can only handle\"\r\n            f\" maximal {num_train_timesteps} timesteps.\"\r\n        )\r\n\r\n    num_inference_steps = num_inference_steps\r\n    # \"linspace\", \"leading\", \"trailing\" corresponds to annotation of Table 2. of https://arxiv.org/abs/2305.08891\r\n    if timestep_spacing == \"linspace\":\r\n        timesteps = (\r\n            np.linspace(0, num_train_timesteps - 1, num_inference_steps)\r\n            .round()[::-1]\r\n            .copy()\r\n            .astype(np.int64)\r\n        )\r\n    elif timestep_spacing == \"leading\":\r\n        step_ratio = num_train_timesteps // num_inference_steps\r\n        # creates integer timesteps by multiplying by ratio\r\n        # casting to int to avoid issues when num_inference_step is power of 3\r\n        timesteps = (\r\n            (np.arange(0, num_inference_steps) * step_ratio)\r\n            .round()[::-1]\r\n            .copy()\r\n            .astype(np.int64)\r\n        )\r\n        # timesteps += steps_offset\r\n    elif timestep_spacing == \"trailing\":\r\n        step_ratio = num_train_timesteps / num_inference_steps\r\n        # creates integer timesteps by multiplying by ratio\r\n        # casting to int to avoid issues when num_inference_step is power of 3\r\n        timesteps = np.round(np.arange(num_train_timesteps, 0, -step_ratio)).astype(\r\n            np.int64\r\n        )\r\n        timesteps -= 1\r\n    else:\r\n        raise ValueError(\r\n            f\"{timestep_spacing} is not supported. Please make sure to choose one of 'leading' or 'trailing'.\"\r\n        )\r\n\r\n    timesteps = torch.from_numpy(timesteps).to(device)\r\n    return timesteps\r\n\r\n\r\ndef get_timesteps(num_inference_steps, timesteps_set, strength, device):\r\n    # get the original timestep using init_timestep\r\n    init_timestep = min(int(num_inference_steps * strength), num_inference_steps)\r\n\r\n    t_start = max(num_inference_steps - init_timestep, 0)\r\n    timesteps = timesteps_set[t_start * 1 :]\r\n\r\n    return timesteps, num_inference_steps - t_start\r\n\r\n\r\ndef get_noised_image_latents(img, W, H, ddim_steps, strength, seed, device):\r\n    img1 = [cv2.resize(img, (W, H))]\r\n    img1 = np.concatenate([i[None, :] for i in img1], axis=0)\r\n    img1 = img1.transpose(0, 3, 1, 2)\r\n    img1 = torch.from_numpy(img1).to(dtype=torch.float32) / 127.5 - 1.0\r\n\r\n    image_latents = model.get_first_stage_encoding(\r\n        model.encode_first_stage(img1.cuda())\r\n    ).detach()\r\n    shape = image_latents.shape\r\n    generator = torch.manual_seed(seed)\r\n\r\n    noise = randn_tensor(shape, generator=generator, device=device, dtype=torch.float32)\r\n\r\n    timesteps_set = set_timesteps(\r\n        ddim_steps, timestep_spacing=\"linspace\", device=device\r\n    )\r\n    timesteps, num_inference_steps = get_timesteps(\r\n        ddim_steps, timesteps_set, strength, device\r\n    )\r\n    latent_timestep = timesteps[1].repeat(1 * 1)\r\n\r\n    init_latents = add_noise(image_latents, noise, torch.tensor(latent_timestep))\r\n    for j in range(0, 1000, 100):\r\n        x_samples = model.decode_first_stage(\r\n            add_noise(image_latents, noise, torch.tensor(j))\r\n        )\r\n        init_image = (\r\n            (einops.rearrange(x_samples, \"b c h w -> b h w c\") * 127.5 + 127.5)\r\n            .cpu()\r\n            .numpy()\r\n            .clip(0, 255)\r\n            .astype(np.uint8)\r\n        )\r\n\r\n        cv2.imwrite(\r\n            \"./initlatents1/\" + str(j) + \"init_image.png\",\r\n            cv2.cvtColor(init_image[0], cv2.COLOR_RGB2BGR),\r\n        )\r\n    return init_latents\r\n\r\n\r\ndef process(\r\n    using_deformable_vae,\r\n    change_according_to_strokes,\r\n    iterative_editing,\r\n    input_image,\r\n    hint_image,\r\n    prompt,\r\n    a_prompt,\r\n    n_prompt,\r\n    num_samples,\r\n    image_resolution,\r\n    ddim_steps,\r\n    guess_mode,\r\n    strength,\r\n    scale,\r\n    sag_scale,\r\n    SAG_influence_step,\r\n    seed,\r\n    eta,\r\n):\r\n    torch.cuda.empty_cache()\r\n    with torch.no_grad():\r\n        ref_flag = True\r\n        input_image_ori = input_image\r\n        if is_gray_scale(input_image):\r\n            print(\"It is a greyscale image.\")\r\n            # mask=get_mask(input_image,hint_image)\r\n        else:\r\n            print(\"It is a color image.\")\r\n            input_image_ori = input_image\r\n            input_image = cv2.cvtColor(input_image, cv2.COLOR_RGB2LAB)[:, :, 0]\r\n            input_image = cv2.merge([input_image, input_image, input_image])\r\n        mask = get_mask(input_image_ori, hint_image)\r\n        cv2.imwrite(\"./logs/gradio_mask1.png\", mask)\r\n\r\n        if iterative_editing:\r\n            mask = 255 - mask\r\n            if change_according_to_strokes:\r\n                kernel = np.ones((15, 15), np.uint8)\r\n                mask = cv2.morphologyEx(mask, cv2.MORPH_GRADIENT, kernel)\r\n                hint_image = mask / 255.0 * hint_image + (1 - mask / 255.0) * hint_image\r\n            else:\r\n                hint_image = (\r\n                    mask / 255.0 * input_image + (1 - mask / 255.0) * input_image_ori\r\n                )\r\n        else:\r\n            hint_image = mask / 255.0 * input_image + (1 - mask / 255.0) * hint_image\r\n        hint_image = hint_image.astype(np.uint8)\r\n        if len(prompt) == 0:\r\n            image = Image.fromarray(input_image)\r\n            image = vis_processors[\"eval\"](image).unsqueeze(0).to(device)\r\n            prompt = BLIP_model.generate({\"image\": image})[0]\r\n            if (\r\n                \"a black and white photo of\" in prompt\r\n                or \"black and white photograph of\" in prompt\r\n            ):\r\n                prompt = prompt.replace(prompt[: prompt.find(\"of\") + 3], \"\")\r\n        print(prompt)\r\n        H_ori, W_ori, C_ori = input_image.shape\r\n        img = resize_image(input_image, image_resolution)\r\n        mask = resize_image(mask, image_resolution)\r\n        hint_image = resize_image(hint_image, image_resolution)\r\n        mask, masked_image = prepare_mask_and_masked_image(\r\n            Image.fromarray(hint_image), Image.fromarray(mask)\r\n        )\r\n        mask, masked_image_latents = encode_mask(mask, masked_image)\r\n        H, W, C = img.shape\r\n\r\n        # if ref_image is None:\r\n        ref_image = np.array([[[0] * C] * W] * H).astype(np.float32)\r\n        # print(ref_image.shape)\r\n        # ref_flag=False\r\n        ref_image = resize_image(ref_image, image_resolution)\r\n\r\n        # cv2.imwrite(\"./logs/exemplar_image.png\",cv2.cvtColor(ref_image,cv2.COLOR_RGB2BGR))\r\n\r\n        # ddim_steps=1\r\n        control = torch.from_numpy(img.copy()).float().cuda() / 255.0\r\n        control = torch.stack([control for _ in range(num_samples)], dim=0)\r\n        control = einops.rearrange(control, \"b h w c -> b c h w\").clone()\r\n\r\n        if seed == -1:\r\n            seed = random.randint(0, 65535)\r\n        seed_everything(seed)\r\n\r\n        ref_image = cv2.resize(ref_image, (W, H))\r\n\r\n        ref_image = torch.from_numpy(ref_image).cuda().unsqueeze(0)\r\n\r\n        init_latents = None\r\n\r\n        if config.save_memory:\r\n            model.low_vram_shift(is_diffusing=False)\r\n\r\n        print(\"no reference images, using Frozen encoder\")\r\n        cond = {\r\n            \"c_concat\": [control],\r\n            \"c_crossattn\": [\r\n                model.get_learned_conditioning([prompt + \", \" + a_prompt] * num_samples)\r\n            ],\r\n        }\r\n        un_cond = {\r\n            \"c_concat\": None if guess_mode else [control],\r\n            \"c_crossattn\": [model.get_learned_conditioning([n_prompt] * num_samples)],\r\n        }\r\n        shape = (4, H // 8, W // 8)\r\n\r\n        if config.save_memory:\r\n            model.low_vram_shift(is_diffusing=True)\r\n        noise = randn_tensor(\r\n            shape, generator=generator, device=device, dtype=torch.float32\r\n        )\r\n        model.control_scales = (\r\n            [strength * (0.825 ** float(12 - i)) for i in range(13)]\r\n            if guess_mode\r\n            else ([strength] * 13)\r\n        )  # Magic number. IDK why. Perhaps because 0.825**12<0.01 but 0.826**12>0.01\r\n        samples, intermediates = ddim_sampler.sample(\r\n            model,\r\n            ddim_steps,\r\n            num_samples,\r\n            shape,\r\n            cond,\r\n            mask=mask,\r\n            masked_image_latents=masked_image_latents,\r\n            verbose=False,\r\n            eta=eta,\r\n            #  x_T=image_latents,\r\n            x_T=init_latents,\r\n            unconditional_guidance_scale=scale,\r\n            sag_scale=sag_scale,\r\n            SAG_influence_step=SAG_influence_step,\r\n            noise=noise,\r\n            unconditional_conditioning=un_cond,\r\n        )\r\n\r\n        if config.save_memory:\r\n            model.low_vram_shift(is_diffusing=False)\r\n\r\n        if not using_deformable_vae:\r\n            x_samples = model.decode_first_stage(samples)\r\n        else:\r\n            samples = model.decode_first_stage_before_vae(samples)\r\n            gray_content_z = vae_model.get_gray_content_z(\r\n                torch.from_numpy(img.copy()).float().cuda() / 255.0\r\n            )\r\n            # print(gray_content_z.shape)\r\n            x_samples = vae_model.decode(samples, gray_content_z)\r\n\r\n        x_samples = (\r\n            (einops.rearrange(x_samples, \"b c h w -> b h w c\") * 127.5 + 127.5)\r\n            .cpu()\r\n            .numpy()\r\n            .clip(0, 255)\r\n            .astype(np.uint8)\r\n        )\r\n\r\n        # single image replace L channel\r\n        results_ori = [x_samples[i] for i in range(num_samples)]\r\n        results_ori = [\r\n            cv2.resize(i, (W_ori, H_ori), interpolation=cv2.INTER_LANCZOS4)\r\n            for i in results_ori\r\n        ]\r\n\r\n        cv2.imwrite(\r\n            \"./logs/result_ori.png\", cv2.cvtColor(results_ori[0], cv2.COLOR_RGB2BGR)\r\n        )\r\n\r\n        results_tmp = [\r\n            cv2.cvtColor(np.array(i), cv2.COLOR_RGB2LAB) for i in results_ori\r\n        ]\r\n        results = [\r\n            cv2.merge([input_image[:, :, 0], tmp[:, :, 1], tmp[:, :, 2]])\r\n            for tmp in results_tmp\r\n        ]\r\n        results_mergeL = [\r\n            cv2.cvtColor(np.asarray(i), cv2.COLOR_LAB2RGB) for i in results\r\n        ]  # cv2.COLOR_LAB2BGR)\r\n        cv2.imwrite(\r\n            \"./logs/output.png\", cv2.cvtColor(results_mergeL[0], cv2.COLOR_RGB2BGR)\r\n        )\r\n    return results_mergeL\r\n\r\n\r\ndef get_grayscale_img(img, progress=gr.Progress(track_tqdm=True)):\r\n    torch.cuda.empty_cache()\r\n    for j in tqdm.tqdm(range(1), desc=\"Uploading input...\"):\r\n        return img, \"Uploading input image done.\"\r\n\r\n\r\nblock = gr.Blocks().queue()\r\nwith block:\r\n    with gr.Row():\r\n        gr.Markdown(\r\n            \"## Control-Color\"\r\n        )  # (\"## Color-Anything\")#Control Stable Diffusion with L channel\r\n    with gr.Row():\r\n        with gr.Column():\r\n            # input_image = gr.Image(source='upload', type=\"numpy\")\r\n            grayscale_img = gr.Image(visible=False, type=\"numpy\")\r\n            input_image = gr.Image(\r\n                source=\"upload\", tool=\"color-sketch\", interactive=True\r\n            )\r\n            Grayscale_button = gr.Button(value=\"Upload input image\")\r\n            text_out = gr.Textbox(\r\n                value=\"Please upload input image first, then draw the strokes or input text prompts or give reference images as you wish.\"\r\n            )\r\n            prompt = gr.Textbox(label=\"Prompt\")\r\n            change_according_to_strokes = gr.Checkbox(\r\n                label=\"Change according to strokes' color\", value=True\r\n            )\r\n            iterative_editing = gr.Checkbox(\r\n                label=\"Only change the strokes' area\", value=False\r\n            )\r\n            using_deformable_vae = gr.Checkbox(\r\n                label=\"Using deformable vae. (Less color overflow)\", value=False\r\n            )\r\n            # with gr.Accordion(\"Input Reference\", open=False):\r\n            #     ref_image = gr.Image(source='upload', type=\"numpy\")\r\n            run_button = gr.Button(\r\n                label=\"Upload prompts/strokes (optional) and Run\",\r\n                value=\"Upload prompts/strokes (optional) and Run\",\r\n            )\r\n            with gr.Accordion(\"Advanced options\", open=False):\r\n                num_samples = gr.Slider(\r\n                    label=\"Images\", minimum=1, maximum=12, value=1, step=1\r\n                )\r\n                image_resolution = gr.Slider(\r\n                    label=\"Image Resolution\",\r\n                    minimum=256,\r\n                    maximum=768,\r\n                    value=512,\r\n                    step=64,\r\n                )\r\n                strength = gr.Slider(\r\n                    label=\"Control Strength\",\r\n                    minimum=0.0,\r\n                    maximum=2.0,\r\n                    value=1.0,\r\n                    step=0.01,\r\n                )\r\n                guess_mode = gr.Checkbox(label=\"Guess Mode\", value=False)\r\n                # detect_resolution = gr.Slider(label=\"Depth Resolution\", minimum=128, maximum=1024, value=384, step=1)\r\n                ddim_steps = gr.Slider(\r\n                    label=\"Steps\", minimum=1, maximum=100, value=20, step=1\r\n                )\r\n                scale = gr.Slider(\r\n                    label=\"Guidance Scale\",\r\n                    minimum=0.1,\r\n                    maximum=30.0,\r\n                    value=7.0,\r\n                    step=0.1,\r\n                )  # value=9.0\r\n                sag_scale = gr.Slider(\r\n                    label=\"SAG Scale\", minimum=0.0, maximum=1.0, value=0.05, step=0.01\r\n                )  # 0.08\r\n                SAG_influence_step = gr.Slider(\r\n                    label=\"1000-SAG influence step\",\r\n                    minimum=0,\r\n                    maximum=900,\r\n                    value=600,\r\n                    step=50,\r\n                )\r\n                seed = gr.Slider(\r\n                    label=\"Seed\", minimum=-1, maximum=2147483647, step=1, randomize=True\r\n                )  # 94433242802\r\n                eta = gr.Number(label=\"eta (DDIM)\", value=0.0)\r\n                a_prompt = gr.Textbox(\r\n                    label=\"Added Prompt\", value=\"best quality, detailed, real\"\r\n                )  # extremely detailed\r\n                n_prompt = gr.Textbox(\r\n                    label=\"Negative Prompt\",\r\n                    value=\"a black and white photo, longbody, lowres, bad anatomy, bad hands, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality\",\r\n                )\r\n        with gr.Column():\r\n            result_gallery = gr.Gallery(\r\n                label=\"Output\", show_label=False, elem_id=\"gallery\"\r\n            ).style(grid=2, height=\"auto\")\r\n            # grayscale_img = gr.Image(interactive=False,visible=False)\r\n\r\n    Grayscale_button.click(\r\n        fn=get_grayscale_img, inputs=input_image, outputs=[grayscale_img, text_out]\r\n    )\r\n    ips = [\r\n        using_deformable_vae,\r\n        change_according_to_strokes,\r\n        iterative_editing,\r\n        grayscale_img,\r\n        input_image,\r\n        prompt,\r\n        a_prompt,\r\n        n_prompt,\r\n        num_samples,\r\n        image_resolution,\r\n        ddim_steps,\r\n        guess_mode,\r\n        strength,\r\n        scale,\r\n        sag_scale,\r\n        SAG_influence_step,\r\n        seed,\r\n        eta,\r\n    ]\r\n    run_button.click(fn=process, inputs=ips, outputs=[result_gallery])\r\n\r\n\r\nblock.launch(server_name=\"0.0.0.0\", share=True)\r\n"}
{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/newCtrlColor/test_rtx3050.py"}, "originalCode": "\"\"\"\nComprehensive RTX 3050 Test for CtrlColor\n\nBased on CUDA OOM analysis from command_logs/test_rtx3050_simple.log:\n- CUDA out of memory when loading 859.54M parameter model\n- 3.35 GiB already allocated out of 4.00 GiB total\n- Need CPU-first loading strategy\n\nThis single file handles all RTX 3050 optimizations and testing.\n\"\"\"\n\nimport torch\nimport torch.nn.functional as F\nimport gc\nimport os\nimport sys\nimport json\nfrom contextlib import contextmanager\n\n# Add paths\nsys.path.append('.')\nsys.path.append('./cldm')\n\nfrom cldm.model import create_model, load_state_dict\nfrom cldm.ddim_hacked_sag import DDIMSampler\n\n\nclass RTX3050Manager:\n    \"\"\"Complete RTX 3050 management and optimization\"\"\"\n    \n    def __init__(self):\n        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n        self.max_memory_gb = 3.4  # 85% of 4GB to avoid OOM\n        self.gpu_name = None\n        self.total_memory_gb = 0\n        \n        if torch.cuda.is_available():\n            self.gpu_name = torch.cuda.get_device_name(0)\n            self.total_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3\n        \n        self.setup_optimizations()\n    \n    def setup_optimizations(self):\n        \"\"\"Apply all RTX 3050 optimizations\"\"\"\n        if torch.cuda.is_available():\n            # Memory management\n            torch.cuda.set_per_process_memory_fraction(0.85)\n            os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'\n            \n            # Performance optimizations\n            torch.backends.cudnn.benchmark = True\n            torch.backends.cudnn.enabled = True\n            \n            # Enable memory efficient attention if available\n            try:\n                torch.backends.cuda.enable_flash_sdp(True)\n            except:\n                pass\n            \n            print(\"✅ RTX 3050 optimizations applied:\")\n            print(f\"   - GPU: {self.gpu_name}\")\n            print(f\"   - Total VRAM: {self.total_memory_gb:.1f}GB\")\n            print(f\"   - Memory limit: {self.max_memory_gb:.1f}GB (85%)\")\n            print(\"   - Memory fragmentation: Reduced\")\n            print(\"   - FP16: Enabled\")\n        else:\n            print(\"⚠️ CUDA not available, using CPU mode\")\n    \n    def get_memory_info(self):\n        \"\"\"Get current GPU memory usage\"\"\"\n        if torch.cuda.is_available():\n            allocated = torch.cuda.memory_allocated() / 1024**3\n            reserved = torch.cuda.memory_reserved() / 1024**3\n            return allocated, reserved\n        return 0, 0\n    \n    def clear_memory(self):\n        \"\"\"Clear GPU cache and run garbage collection\"\"\"\n        if torch.cuda.is_available():\n            torch.cuda.empty_cache()\n        gc.collect()\n    \n    @contextmanager\n    def memory_efficient_context(self):\n        \"\"\"Context manager for memory-efficient operations\"\"\"\n        self.clear_memory()\n        try:\n            yield\n        finally:\n            self.clear_memory()\n    \n    def check_memory_available(self, required_gb):\n        \"\"\"Check if enough memory is available\"\"\"\n        allocated, _ = self.get_memory_info()\n        available = self.max_memory_gb - allocated\n        return available >= required_gb\n    \n    def load_model_safe(self, config_path, ckpt_path):\n        \"\"\"\n        Safe model loading that addresses the CUDA OOM issue\n        \n        Strategy:\n        1. Load everything on CPU first\n        2. Estimate memory requirements\n        3. Transfer to GPU only if safe\n        4. Use FP16 to reduce memory usage\n        \"\"\"\n        print(\"🔧 Loading model with RTX 3050 safe strategy...\")\n        \n        with self.memory_efficient_context():\n            try:\n                # Step 1: Create model on CPU\n                print(\"   - Creating model on CPU...\")\n                model = create_model(config_path).cpu()\n                \n                # Step 2: Load checkpoint to CPU memory\n                print(\"   - Loading checkpoint to CPU...\")\n                checkpoint = torch.load(ckpt_path, map_location='cpu')\n                \n                # Step 3: Load state dict on CPU\n                print(\"   - Loading state dict on CPU...\")\n                if isinstance(checkpoint, dict) and 'state_dict' in checkpoint:\n                    state_dict = checkpoint['state_dict']\n                else:\n                    state_dict = checkpoint\n                \n                model.load_state_dict(state_dict, strict=False)\n                \n                # Clean up checkpoint to free CPU memory\n                del checkpoint, state_dict\n                self.clear_memory()\n                \n                # Step 4: Estimate model memory requirements\n                param_count = sum(p.numel() for p in model.parameters())\n                model_size_gb = param_count * 4 / 1024**3  # FP32 size\n                model_size_fp16_gb = param_count * 2 / 1024**3  # FP16 size\n                \n                print(f\"   - Model parameters: {param_count:,}\")\n                print(f\"   - Estimated FP32 size: {model_size_gb:.2f}GB\")\n                print(f\"   - Estimated FP16 size: {model_size_fp16_gb:.2f}GB\")\n                \n                # Step 5: Try GPU transfer\n                if torch.cuda.is_available():\n                    allocated_before, _ = self.get_memory_info()\n                    print(f\"   - GPU memory before transfer: {allocated_before:.2f}GB\")\n                    \n                    if self.check_memory_available(model_size_fp16_gb):\n                        print(\"   - Transferring to GPU with FP16...\")\n                        model = model.cuda().half()\n                        \n                        allocated_after, _ = self.get_memory_info()\n                        print(f\"   - GPU memory after transfer: {allocated_after:.2f}GB\")\n                        print(\"   ✅ Model loaded on GPU successfully\")\n                    else:\n                        print(\"   ⚠️ Insufficient GPU memory, keeping on CPU\")\n                        print(\"   - Will use CPU inference (slower but functional)\")\n                \n                return model\n                \n            except RuntimeError as e:\n                if \"out of memory\" in str(e):\n                    print(f\"   ❌ CUDA OOM during loading: {e}\")\n                    print(\"   - Falling back to CPU...\")\n                    try:\n                        model = model.cpu()\n                        return model\n                    except:\n                        return None\n                else:\n                    print(f\"   ❌ Model loading failed: {e}\")\n                    return None\n            \n            except Exception as e:\n                print(f\"   ❌ Unexpected error: {e}\")\n                return None\n    \n    def test_inference(self, model):\n        \"\"\"Test inference with memory monitoring\"\"\"\n        print(\"🧪 Testing inference with memory monitoring...\")\n        \n        try:\n            with self.memory_efficient_context():\n                device = next(model.parameters()).device\n                dtype = next(model.parameters()).dtype\n                \n                # Create minimal test inputs\n                batch_size = 1\n                height, width = 256, 256  # Reduced size for memory safety\n                \n                print(f\"   - Device: {device}\")\n                print(f\"   - Dtype: {dtype}\")\n                print(f\"   - Test size: {height}x{width}\")\n                \n                # Monitor memory before\n                allocated_before, _ = self.get_memory_info()\n                print(f\"   - Memory before: {allocated_before:.2f}GB\")\n                \n                # Create test inputs\n                hint = torch.randn(batch_size, 5, height, width, device=device, dtype=dtype)\n                \n                # Test forward pass\n                with torch.no_grad():\n                    if hasattr(model, 'apply_model'):\n                        # ControlLDM interface\n                        timesteps = torch.randint(0, 1000, (batch_size,), device=device)\n                        noise = torch.randn(batch_size, 4, height//8, width//8, device=device, dtype=dtype)\n                        \n                        cond = {\n                            \"c_concat\": [hint],\n                            \"c_crossattn\": [[\"test prompt\"]]\n                        }\n                        \n                        output = model.apply_model(noise, timesteps, cond)\n                        print(f\"   - Forward pass successful: {output.shape}\")\n                    else:\n                        # Basic forward\n                        output = model(hint)\n                        print(f\"   - Basic forward successful: {output.shape}\")\n                \n                # Monitor memory after\n                allocated_after, _ = self.get_memory_info()\n                print(f\"   - Memory after: {allocated_after:.2f}GB\")\n                print(f\"   - Memory increase: {allocated_after - allocated_before:.2f}GB\")\n                \n                print(\"   ✅ Inference test successful!\")\n                return True\n                \n        except RuntimeError as e:\n            if \"out of memory\" in str(e):\n                print(f\"   ❌ CUDA OOM during inference: {e}\")\n                print(\"   💡 Try reducing image size or using CPU\")\n            else:\n                print(f\"   ❌ Inference error: {e}\")\n            return False\n        \n        except Exception as e:\n            print(f\"   ❌ Unexpected inference error: {e}\")\n            return False\n    \n    def run_complete_test(self):\n        \"\"\"Run complete RTX 3050 test suite\"\"\"\n        print(\"🚀 RTX 3050 Complete Test Suite\")\n        print(\"=\" * 60)\n        \n        # Configuration\n        config_path = './models/cldm_v15_inpainting_infer1.yaml'\n        ckpt_path = './pretrained_models/main_model.ckpt'\n        \n        # Check files exist\n        if not os.path.exists(config_path):\n            print(f\"❌ Config file not found: {config_path}\")\n            return False\n        \n        if not os.path.exists(ckpt_path):\n            print(f\"❌ Checkpoint file not found: {ckpt_path}\")\n            return False\n        \n        # Test 1: Model loading\n        print(\"\\n📋 Test 1: Safe Model Loading\")\n        model = self.load_model_safe(config_path, ckpt_path)\n        if model is None:\n            print(\"❌ Model loading failed\")\n            return False\n        \n        # Test 2: Inference\n        print(\"\\n📋 Test 2: Memory-Safe Inference\")\n        inference_success = self.test_inference(model)\n        \n        # Test 3: Memory efficiency\n        print(\"\\n📋 Test 3: Memory Efficiency Report\")\n        allocated, reserved = self.get_memory_info()\n        efficiency = (allocated / self.max_memory_gb) * 100 if allocated > 0 else 0\n        \n        print(f\"   - Memory allocated: {allocated:.2f}GB\")\n        print(f\"   - Memory reserved: {reserved:.2f}GB\")\n        print(f\"   - Memory efficiency: {efficiency:.1f}% of limit\")\n        \n        # Final result\n        print(\"\\n\" + \"=\" * 60)\n        if inference_success:\n            print(\"✅ RTX 3050 test PASSED!\")\n            print(\"💡 CtrlColor can run on RTX 3050 with optimizations\")\n            \n            # Save test results\n            results = {\n                \"gpu\": self.gpu_name,\n                \"total_memory_gb\": self.total_memory_gb,\n                \"memory_limit_gb\": self.max_memory_gb,\n                \"memory_allocated_gb\": allocated,\n                \"memory_efficiency_percent\": efficiency,\n                \"model_loading\": \"success\",\n                \"inference\": \"success\",\n                \"overall\": \"passed\"\n            }\n            \n            with open('rtx3050_test_results.json', 'w') as f:\n                json.dump(results, f, indent=2)\n            \n            print(\"📊 Test results saved to rtx3050_test_results.json\")\n            \n        else:\n            print(\"❌ RTX 3050 test FAILED!\")\n            print(\"💡 Consider using CPU mode or smaller models\")\n        \n        return inference_success\n\n\ndef main():\n    \"\"\"Main test function\"\"\"\n    try:\n        manager = RTX3050Manager()\n        success = manager.run_complete_test()\n        return success\n    except Exception as e:\n        print(f\"❌ Test failed with error: {e}\")\n        return False\n\n\nif __name__ == \"__main__\":\n    success = main()\n    sys.exit(0 if success else 1)\n", "modifiedCode": "\"\"\"\nComprehensive RTX 3050 Test for CtrlColor\n\nBased on CUDA OOM analysis from command_logs/test_rtx3050_simple.log:\n- CUDA out of memory when loading 859.54M parameter model\n- 3.35 GiB already allocated out of 4.00 GiB total\n- Need CPU-first loading strategy\n\nThis single file handles all RTX 3050 optimizations and testing.\n\"\"\"\n\nimport torch\nimport torch.nn.functional as F\nimport gc\nimport os\nimport sys\nimport json\nfrom contextlib import contextmanager\n\n# Add paths\nsys.path.append('.')\nsys.path.append('./cldm')\n\nfrom cldm.model import create_model, load_state_dict\nfrom cldm.ddim_hacked_sag import DDIMSampler\n\n\nclass RTX3050Manager:\n    \"\"\"Complete RTX 3050 management and optimization\"\"\"\n    \n    def __init__(self):\n        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n        self.max_memory_gb = 3.4  # 85% of 4GB to avoid OOM\n        self.gpu_name = None\n        self.total_memory_gb = 0\n        \n        if torch.cuda.is_available():\n            self.gpu_name = torch.cuda.get_device_name(0)\n            self.total_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3\n        \n        self.setup_optimizations()\n    \n    def setup_optimizations(self):\n        \"\"\"Apply all RTX 3050 optimizations\"\"\"\n        if torch.cuda.is_available():\n            # Memory management\n            torch.cuda.set_per_process_memory_fraction(0.85)\n            os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'\n            \n            # Performance optimizations\n            torch.backends.cudnn.benchmark = True\n            torch.backends.cudnn.enabled = True\n            \n            # Enable memory efficient attention if available\n            try:\n                torch.backends.cuda.enable_flash_sdp(True)\n            except:\n                pass\n            \n            print(\"✅ RTX 3050 optimizations applied:\")\n            print(f\"   - GPU: {self.gpu_name}\")\n            print(f\"   - Total VRAM: {self.total_memory_gb:.1f}GB\")\n            print(f\"   - Memory limit: {self.max_memory_gb:.1f}GB (85%)\")\n            print(\"   - Memory fragmentation: Reduced\")\n            print(\"   - FP16: Enabled\")\n        else:\n            print(\"⚠️ CUDA not available, using CPU mode\")\n    \n    def get_memory_info(self):\n        \"\"\"Get current GPU memory usage\"\"\"\n        if torch.cuda.is_available():\n            allocated = torch.cuda.memory_allocated() / 1024**3\n            reserved = torch.cuda.memory_reserved() / 1024**3\n            return allocated, reserved\n        return 0, 0\n    \n    def clear_memory(self):\n        \"\"\"Clear GPU cache and run garbage collection\"\"\"\n        if torch.cuda.is_available():\n            torch.cuda.empty_cache()\n        gc.collect()\n    \n    @contextmanager\n    def memory_efficient_context(self):\n        \"\"\"Context manager for memory-efficient operations\"\"\"\n        self.clear_memory()\n        try:\n            yield\n        finally:\n            self.clear_memory()\n    \n    def check_memory_available(self, required_gb):\n        \"\"\"Check if enough memory is available\"\"\"\n        allocated, _ = self.get_memory_info()\n        available = self.max_memory_gb - allocated\n        return available >= required_gb\n    \n    def load_model_safe(self, config_path, ckpt_path):\n        \"\"\"\n        Safe model loading that addresses the CUDA OOM issue\n        \n        Strategy:\n        1. Load everything on CPU first\n        2. Estimate memory requirements\n        3. Transfer to GPU only if safe\n        4. Use FP16 to reduce memory usage\n        \"\"\"\n        print(\"🔧 Loading model with RTX 3050 safe strategy...\")\n        \n        with self.memory_efficient_context():\n            try:\n                # Step 1: Create model on CPU\n                print(\"   - Creating model on CPU...\")\n                model = create_model(config_path).cpu()\n                \n                # Step 2: Load checkpoint to CPU memory\n                print(\"   - Loading checkpoint to CPU...\")\n                checkpoint = torch.load(ckpt_path, map_location='cpu')\n                \n                # Step 3: Load state dict on CPU\n                print(\"   - Loading state dict on CPU...\")\n                if isinstance(checkpoint, dict) and 'state_dict' in checkpoint:\n                    state_dict = checkpoint['state_dict']\n                else:\n                    state_dict = checkpoint\n                \n                model.load_state_dict(state_dict, strict=False)\n                \n                # Clean up checkpoint to free CPU memory\n                del checkpoint, state_dict\n                self.clear_memory()\n                \n                # Step 4: Estimate model memory requirements\n                param_count = sum(p.numel() for p in model.parameters())\n                model_size_gb = param_count * 4 / 1024**3  # FP32 size\n                model_size_fp16_gb = param_count * 2 / 1024**3  # FP16 size\n                \n                print(f\"   - Model parameters: {param_count:,}\")\n                print(f\"   - Estimated FP32 size: {model_size_gb:.2f}GB\")\n                print(f\"   - Estimated FP16 size: {model_size_fp16_gb:.2f}GB\")\n                \n                # Step 5: Try GPU transfer\n                if torch.cuda.is_available():\n                    allocated_before, _ = self.get_memory_info()\n                    print(f\"   - GPU memory before transfer: {allocated_before:.2f}GB\")\n                    \n                    if self.check_memory_available(model_size_fp16_gb):\n                        print(\"   - Transferring to GPU with FP16...\")\n                        model = model.cuda().half()\n                        \n                        allocated_after, _ = self.get_memory_info()\n                        print(f\"   - GPU memory after transfer: {allocated_after:.2f}GB\")\n                        print(\"   ✅ Model loaded on GPU successfully\")\n                    else:\n                        print(\"   ⚠️ Insufficient GPU memory, keeping on CPU\")\n                        print(\"   - Will use CPU inference (slower but functional)\")\n                \n                return model\n                \n            except RuntimeError as e:\n                if \"out of memory\" in str(e):\n                    print(f\"   ❌ CUDA OOM during loading: {e}\")\n                    print(\"   - Falling back to CPU...\")\n                    try:\n                        model = model.cpu()\n                        return model\n                    except:\n                        return None\n                else:\n                    print(f\"   ❌ Model loading failed: {e}\")\n                    return None\n            \n            except Exception as e:\n                print(f\"   ❌ Unexpected error: {e}\")\n                return None\n    \n    def test_inference(self, model):\n        \"\"\"Test inference with memory monitoring\"\"\"\n        print(\"🧪 Testing inference with memory monitoring...\")\n        \n        try:\n            with self.memory_efficient_context():\n                device = next(model.parameters()).device\n                dtype = next(model.parameters()).dtype\n                \n                # Create minimal test inputs\n                batch_size = 1\n                height, width = 256, 256  # Reduced size for memory safety\n                \n                print(f\"   - Device: {device}\")\n                print(f\"   - Dtype: {dtype}\")\n                print(f\"   - Test size: {height}x{width}\")\n                \n                # Monitor memory before\n                allocated_before, _ = self.get_memory_info()\n                print(f\"   - Memory before: {allocated_before:.2f}GB\")\n                \n                # Create test inputs\n                hint = torch.randn(batch_size, 5, height, width, device=device, dtype=dtype)\n                \n                # Test forward pass\n                with torch.no_grad():\n                    if hasattr(model, 'apply_model'):\n                        # ControlLDM interface\n                        timesteps = torch.randint(0, 1000, (batch_size,), device=device)\n                        noise = torch.randn(batch_size, 4, height//8, width//8, device=device, dtype=dtype)\n                        \n                        cond = {\n                            \"c_concat\": [hint],\n                            \"c_crossattn\": [[\"test prompt\"]]\n                        }\n                        \n                        output = model.apply_model(noise, timesteps, cond)\n                        print(f\"   - Forward pass successful: {output.shape}\")\n                    else:\n                        # Basic forward\n                        output = model(hint)\n                        print(f\"   - Basic forward successful: {output.shape}\")\n                \n                # Monitor memory after\n                allocated_after, _ = self.get_memory_info()\n                print(f\"   - Memory after: {allocated_after:.2f}GB\")\n                print(f\"   - Memory increase: {allocated_after - allocated_before:.2f}GB\")\n                \n                print(\"   ✅ Inference test successful!\")\n                return True\n                \n        except RuntimeError as e:\n            if \"out of memory\" in str(e):\n                print(f\"   ❌ CUDA OOM during inference: {e}\")\n                print(\"   💡 Try reducing image size or using CPU\")\n            else:\n                print(f\"   ❌ Inference error: {e}\")\n            return False\n        \n        except Exception as e:\n            print(f\"   ❌ Unexpected inference error: {e}\")\n            return False\n    \n    def run_complete_test(self):\n        \"\"\"Run complete RTX 3050 test suite\"\"\"\n        print(\"🚀 RTX 3050 Complete Test Suite\")\n        print(\"=\" * 60)\n        \n        # Configuration\n        config_path = './models/cldm_v15_inpainting_infer1.yaml'\n        ckpt_path = './pretrained_models/main_model.ckpt'\n        \n        # Check files exist\n        if not os.path.exists(config_path):\n            print(f\"❌ Config file not found: {config_path}\")\n            return False\n        \n        if not os.path.exists(ckpt_path):\n            print(f\"❌ Checkpoint file not found: {ckpt_path}\")\n            return False\n        \n        # Test 1: Model loading\n        print(\"\\n📋 Test 1: Safe Model Loading\")\n        model = self.load_model_safe(config_path, ckpt_path)\n        if model is None:\n            print(\"❌ Model loading failed\")\n            return False\n        \n        # Test 2: Inference\n        print(\"\\n📋 Test 2: Memory-Safe Inference\")\n        inference_success = self.test_inference(model)\n        \n        # Test 3: Memory efficiency\n        print(\"\\n📋 Test 3: Memory Efficiency Report\")\n        allocated, reserved = self.get_memory_info()\n        efficiency = (allocated / self.max_memory_gb) * 100 if allocated > 0 else 0\n        \n        print(f\"   - Memory allocated: {allocated:.2f}GB\")\n        print(f\"   - Memory reserved: {reserved:.2f}GB\")\n        print(f\"   - Memory efficiency: {efficiency:.1f}% of limit\")\n        \n        # Final result\n        print(\"\\n\" + \"=\" * 60)\n        if inference_success:\n            print(\"✅ RTX 3050 test PASSED!\")\n            print(\"💡 CtrlColor can run on RTX 3050 with optimizations\")\n            \n            # Save test results\n            results = {\n                \"gpu\": self.gpu_name,\n                \"total_memory_gb\": self.total_memory_gb,\n                \"memory_limit_gb\": self.max_memory_gb,\n                \"memory_allocated_gb\": allocated,\n                \"memory_efficiency_percent\": efficiency,\n                \"model_loading\": \"success\",\n                \"inference\": \"success\",\n                \"overall\": \"passed\"\n            }\n            \n            with open('rtx3050_test_results.json', 'w') as f:\n                json.dump(results, f, indent=2)\n            \n            print(\"📊 Test results saved to rtx3050_test_results.json\")\n            \n        else:\n            print(\"❌ RTX 3050 test FAILED!\")\n            print(\"💡 Consider using CPU mode or smaller models\")\n        \n        return inference_success\n\n\ndef main():\n    \"\"\"Main test function\"\"\"\n    try:\n        manager = RTX3050Manager()\n        success = manager.run_complete_test()\n        return success\n    except Exception as e:\n        print(f\"❌ Test failed with error: {e}\")\n        return False\n\n\nif __name__ == \"__main__\":\n    success = main()\n    sys.exit(0 if success else 1)\n"}
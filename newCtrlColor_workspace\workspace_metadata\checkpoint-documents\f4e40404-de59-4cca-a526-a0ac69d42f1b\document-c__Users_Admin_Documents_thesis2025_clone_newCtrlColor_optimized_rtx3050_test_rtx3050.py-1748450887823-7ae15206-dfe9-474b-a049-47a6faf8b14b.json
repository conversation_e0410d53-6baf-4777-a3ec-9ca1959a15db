{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\newCtrlColor\\optimized_rtx3050\\test_rtx3050.py"}, "originalCode": "\"\"\"\nRTX 3050 Optimized Test Script for CtrlColor\n\nThis is a modified copy of the original test.py with RTX 3050 specific optimizations:\n- FP16 mixed precision\n- Memory management\n- Optimal batch sizes\n- GPU memory monitoring\n- Automatic fallback strategies\n\nBased on: clone/newCtrlColor/test.py\nOptimized for: NVIDIA GeForce RTX 3050 Laptop GPU (4.3GB VRAM)\n\"\"\"\n\nimport random\nimport sys\n\nimport cv2\nimport einops\nimport gradio as gr\nimport numpy as np\nimport psutil\nimport torch\nimport tqdm\nfrom PIL import Image\nfrom pytorch_lightning import seed_everything\n\n# Import RTX 3050 optimized config\nfrom .config_rtx3050 import (\n    DEFAULT_IMAGE_RESOLUTION,\n    DEVICE,\n    USE_FP16,\n    RTX3050AutocastManager,\n    RTX3050MemoryManager,\n    clear_gpu_cache,\n    get_device_info,\n    get_optimal_batch_size,\n    get_optimal_image_resolution,\n)\n\n# Original imports (preserved)\nsys.path.append(\"..\")\nfrom lavis.models import load_model_and_preprocess\n\nimport config\nfrom annotator.util import resize_image\nfrom cldm.ddim_haced_sag_step import DDIMSampler\nfrom cldm.model import create_model, load_state_dict\nfrom ldm.models.autoencoder_train import AutoencoderKL\nfrom share import *\n\n# ============================================================================\n# RTX 3050 OPTIMIZED MODEL LOADING\n# ============================================================================\n\n\ndef load_model_rtx3050():\n    \"\"\"Load CtrlColor model with RTX 3050 optimizations\"\"\"\n    print(\"🎯 Loading CtrlColor model with RTX 3050 optimizations...\")\n\n    # Model paths\n    ckpt_path = \"./pretrained_models/main_model.ckpt\"\n    config_path = \"./models/cldm_v15_inpainting_infer1.yaml\"\n\n    with RTX3050MemoryManager():\n        # Load model on CPU first to save VRAM\n        model = create_model(config_path).cpu()\n\n        # Load state dict with memory optimization\n        state_dict = load_state_dict(ckpt_path, location=\"cpu\")\n        model.load_state_dict(state_dict, strict=False)\n\n        # Move to GPU with FP16 if enabled\n        model = model.to(DEVICE)\n        if USE_FP16:\n            model = model.half()\n\n        print(f\"✅ Model loaded on {DEVICE} with FP16: {USE_FP16}\")\n\n        return model\n\n\ndef load_vae_rtx3050():\n    \"\"\"Load VAE model with RTX 3050 optimizations\"\"\"\n    print(\"🎯 Loading VAE model with RTX 3050 optimizations...\")\n\n    vae_model_ckpt_path = \"./pretrained_models/content-guided_deformable_vae.ckpt\"\n\n    init_config = {\n        \"embed_dim\": 4,\n        \"monitor\": \"val/rec_loss\",\n        \"ddconfig\": {\n            \"double_z\": True,\n            \"z_channels\": 4,\n            \"resolution\": 256,\n            \"in_channels\": 3,\n            \"out_ch\": 3,\n            \"ch\": 128,\n            \"ch_mult\": [1, 2, 4, 4],\n            \"num_res_blocks\": 2,\n            \"attn_resolutions\": [],\n            \"dropout\": 0.0,\n        },\n        \"lossconfig\": {\n            \"target\": \"ldm.modules.losses.LPIPSWithDiscriminator\",\n            \"params\": {\n                \"disc_start\": 501,\n                \"kl_weight\": 0,\n                \"disc_weight\": 0.025,\n                \"disc_factor\": 1.0,\n            },\n        },\n    }\n\n    with RTX3050MemoryManager():\n        vae = AutoencoderKL(**init_config)\n        vae.load_state_dict(load_state_dict(vae_model_ckpt_path, location=\"cpu\"))\n        vae = vae.to(DEVICE)\n\n        if USE_FP16:\n            vae = vae.half()\n\n        print(f\"✅ VAE loaded on {DEVICE} with FP16: {USE_FP16}\")\n\n        return vae\n\n\ndef load_blip_rtx3050():\n    \"\"\"Load BLIP model with RTX 3050 optimizations\"\"\"\n    print(\"🎯 Loading BLIP model with RTX 3050 optimizations...\")\n\n    try:\n        BLIP_model, vis_processors, _ = load_model_and_preprocess(\n            name=\"blip_caption\", model_type=\"base_coco\", is_eval=True, device=DEVICE\n        )\n\n        if USE_FP16:\n            BLIP_model = BLIP_model.half()\n\n        print(\"✅ BLIP model loaded successfully\")\n        return BLIP_model, vis_processors\n\n    except Exception as e:\n        print(f\"⚠️ BLIP loading failed: {e}\")\n        return None, None\n\n\n# ============================================================================\n# INITIALIZE MODELS WITH RTX 3050 OPTIMIZATIONS\n# ============================================================================\n\nprint(\"🚀 Initializing CtrlColor with RTX 3050 optimizations...\")\nprint(f\"📊 Device info: {get_device_info()}\")\n\n# Load models with optimizations\nmodel = load_model_rtx3050()\nddim_sampler = DDIMSampler(model)\nvae_model = load_vae_rtx3050()\nBLIP_model, vis_processors = load_blip_rtx3050()\n\n# Set generator seed\ngenerator = torch.manual_seed(859311133)\n\n# ============================================================================\n# RTX 3050 OPTIMIZED UTILITY FUNCTIONS\n# ============================================================================\n\n\ndef monitor_memory_usage():\n    \"\"\"Monitor GPU and RAM memory usage\"\"\"\n    if torch.cuda.is_available():\n        gpu_memory = torch.cuda.memory_allocated() / 1024**3\n        gpu_total = torch.cuda.get_device_properties(0).total_memory / 1024**3\n        gpu_percent = (gpu_memory / gpu_total) * 100\n    else:\n        gpu_memory = gpu_total = gpu_percent = 0\n\n    ram_percent = psutil.virtual_memory().percent\n\n    return {\n        \"gpu_memory_gb\": gpu_memory,\n        \"gpu_total_gb\": gpu_total,\n        \"gpu_percent\": gpu_percent,\n        \"ram_percent\": ram_percent,\n    }\n\n\ndef adaptive_image_resolution(input_image, conservative=True):\n    \"\"\"Adaptively choose image resolution based on memory\"\"\"\n    memory_info = monitor_memory_usage()\n\n    # If GPU memory usage is high, use conservative resolution\n    if memory_info[\"gpu_percent\"] > 70 or memory_info[\"ram_percent\"] > 85:\n        return 256  # Very conservative\n    elif conservative:\n        return DEFAULT_IMAGE_RESOLUTION  # 512\n    else:\n        return get_optimal_image_resolution(conservative=False)  # 768\n\n\ndef encode_mask_rtx3050(mask, masked_image):\n    \"\"\"RTX 3050 optimized mask encoding\"\"\"\n    with RTX3050AutocastManager():\n        mask = torch.nn.functional.interpolate(\n            mask, size=(mask.shape[2] // 8, mask.shape[3] // 8)\n        )\n        mask = mask.to(device=DEVICE)\n\n        # Use autocast for VAE encoding\n        with torch.cuda.amp.autocast(enabled=USE_FP16):\n            masked_image_latents = model.get_first_stage_encoding(\n                model.encode_first_stage(masked_image.to(DEVICE))\n            ).detach()\n\n        return mask, masked_image_latents\n\n\n# ============================================================================\n# PRESERVED ORIGINAL FUNCTIONS (with minor optimizations)\n# ============================================================================\n\n\ndef get_mask(input_image, hint_image):\n    \"\"\"Get mask from input and hint images (preserved from original)\"\"\"\n    mask = input_image.copy()\n    H, W, C = input_image.shape\n\n    for i in range(H):\n        for j in range(W):\n            if input_image[i, j, 0] == hint_image[i, j, 0]:\n                mask[i, j, :] = 255.0\n            else:\n                mask[i, j, :] = 0.0\n\n    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))\n    mask = cv2.morphologyEx(np.array(mask), cv2.MORPH_OPEN, kernel, iterations=1)\n    return mask\n\n\ndef prepare_mask_and_masked_image(image, mask):\n    \"\"\"Prepare mask and masked image (preserved from original)\"\"\"\n    # [Original function implementation preserved]\n    # This is a complex function, keeping original logic\n\n    if isinstance(image, torch.Tensor):\n        if not isinstance(mask, torch.Tensor):\n            raise TypeError(\n                f\"`image` is a torch.Tensor but `mask` (type: {type(mask)} is not\"\n            )\n\n        # Batch single image\n        if image.ndim == 3:\n            assert image.shape[0] == 3, (\n                \"Image outside a batch should be of shape (3, H, W)\"\n            )\n            image = image.unsqueeze(0)\n\n        # Batch and add channel dim for single mask\n        if mask.ndim == 2:\n            mask = mask.unsqueeze(0).unsqueeze(0)\n\n        # Batch single mask or add channel dim\n        if mask.ndim == 3:\n            if mask.shape[0] == 1:\n                mask = mask.unsqueeze(0)\n            else:\n                mask = mask.unsqueeze(1)\n\n        assert image.ndim == 4 and mask.ndim == 4, (\n            \"Image and Mask must have 4 dimensions\"\n        )\n        assert image.shape[-2:] == mask.shape[-2:], (\n            \"Image and Mask must have the same spatial dimensions\"\n        )\n        assert image.shape[0] == mask.shape[0], (\n            \"Image and Mask must have the same batch size\"\n        )\n\n        # Check image is in [-1, 1]\n        if image.min() < -1 or image.max() > 1:\n            raise ValueError(\"Image should be in [-1, 1] range\")\n\n        # Check mask is in [0, 1]\n        if mask.min() < 0 or mask.max() > 1:\n            raise ValueError(\"Mask should be in [0, 1] range\")\n\n        # Binarize mask\n        mask[mask < 0.5] = 0\n        mask[mask >= 0.5] = 1\n\n        # Image as float32\n        image = image.to(dtype=torch.float32)\n    elif isinstance(mask, torch.Tensor):\n        raise TypeError(\n            f\"`mask` is a torch.Tensor but `image` (type: {type(image)} is not\"\n        )\n    else:\n        # preprocess image\n        if isinstance(image, (Image.Image, np.ndarray)):\n            image = [image]\n\n        if isinstance(image, list) and isinstance(image[0], Image.Image):\n            image = [np.array(i.convert(\"RGB\"))[None, :] for i in image]\n            image = np.concatenate(image, axis=0)\n        elif isinstance(image, list) and isinstance(image[0], np.ndarray):\n            image = np.concatenate([i[None, :] for i in image], axis=0)\n\n        image = image.transpose(0, 3, 1, 2)\n        image = torch.from_numpy(image).to(dtype=torch.float32) / 127.5 - 1.0\n\n        # preprocess mask\n        if isinstance(mask, (Image.Image, np.ndarray)):\n            mask = [mask]\n\n        if isinstance(mask, list) and isinstance(mask[0], Image.Image):\n            mask = np.concatenate(\n                [np.array(m.convert(\"L\"))[None, None, :] for m in mask], axis=0\n            )\n            mask = mask.astype(np.float32) / 255.0\n        elif isinstance(mask, list) and isinstance(mask[0], np.ndarray):\n            mask = np.concatenate([m[None, None, :] for m in mask], axis=0)\n\n        mask[mask < 0.5] = 0\n        mask[mask >= 0.5] = 1\n        mask = torch.from_numpy(mask)\n\n    masked_image = image * (mask < 0.5)\n    return mask, masked_image\n\n\ndef is_gray_scale(img, threshold=10):\n    \"\"\"Check if image is grayscale (preserved from original)\"\"\"\n    img = Image.fromarray(img)\n    if len(img.getbands()) == 1:\n        return True\n\n    img1 = np.asarray(img.getchannel(channel=0), dtype=np.int16)\n    img2 = np.asarray(img.getchannel(channel=1), dtype=np.int16)\n    img3 = np.asarray(img.getchannel(channel=2), dtype=np.int16)\n\n    diff1 = (img1 - img2).var()\n    diff2 = (img2 - img3).var()\n    diff3 = (img3 - img1).var()\n    diff_sum = (diff1 + diff2 + diff3) / 3.0\n\n    return diff_sum <= threshold\n\n\ndef randn_tensor(shape, generator=None, device=None, dtype=None, layout=None):\n    \"\"\"Create random tensor (preserved from original with RTX 3050 optimizations)\"\"\"\n    rand_device = device\n    batch_size = shape[0]\n\n    layout = layout or torch.strided\n    device = device or torch.device(\"cpu\")\n\n    if generator is not None:\n        gen_device_type = (\n            generator.device.type\n            if not isinstance(generator, list)\n            else generator[0].device.type\n        )\n        if gen_device_type != device.type and gen_device_type == \"cpu\":\n            rand_device = \"cpu\"\n            if device != \"mps\":\n                print(\n                    \"The passed generator was created on 'cpu' even though a tensor on {device} was expected.\"\n                )\n        elif gen_device_type != device.type and gen_device_type == \"cuda\":\n            raise ValueError(\n                f\"Cannot generate a {device} tensor from a generator of type {gen_device_type}.\"\n            )\n\n    # make sure generator list of length 1 is treated like a non-list\n    if isinstance(generator, list) and len(generator) == 1:\n        generator = generator[0]\n\n    if isinstance(generator, list):\n        shape = (1,) + shape[1:]\n        latents = [\n            torch.randn(\n                shape,\n                generator=generator[i],\n                device=rand_device,\n                dtype=dtype,\n                layout=layout,\n            )\n            for i in range(batch_size)\n        ]\n        latents = torch.cat(latents, dim=0).to(device)\n    else:\n        latents = torch.randn(\n            shape, generator=generator, device=rand_device, dtype=dtype, layout=layout\n        ).to(device)\n\n    return latents\n\n\n# ============================================================================\n# RTX 3050 OPTIMIZED MAIN PROCESSING FUNCTION\n# ============================================================================\n\n\ndef process_rtx3050(\n    using_deformable_vae,\n    change_according_to_strokes,\n    iterative_editing,\n    input_image,\n    hint_image,\n    prompt,\n    a_prompt,\n    n_prompt,\n    num_samples,\n    image_resolution,\n    ddim_steps,\n    guess_mode,\n    strength,\n    scale,\n    sag_scale,\n    SAG_influence_step,\n    seed,\n    eta,\n):\n    \"\"\"\n    RTX 3050 optimized main processing function\n\n    Key optimizations:\n    - Memory management with context managers\n    - FP16 mixed precision\n    - Adaptive batch sizing\n    - Memory monitoring\n    - Automatic fallback strategies\n    \"\"\"\n\n    print(\"🎯 Starting RTX 3050 optimized processing...\")\n    memory_start = monitor_memory_usage()\n    print(\n        f\"📊 Initial memory: GPU {memory_start['gpu_percent']:.1f}%, RAM {memory_start['ram_percent']:.1f}%\"\n    )\n\n    # Clear cache before processing\n    clear_gpu_cache()\n\n    with RTX3050MemoryManager(clear_cache_before=True, clear_cache_after=True):\n        with torch.no_grad():\n            # Adaptive settings based on memory\n            if memory_start[\"gpu_percent\"] > 70:\n                print(\"⚠️ High GPU memory usage detected, using conservative settings\")\n                num_samples = min(num_samples, 1)\n                image_resolution = min(image_resolution, 256)\n\n            # Use optimal batch size\n            num_samples = min(num_samples, get_optimal_batch_size(\"inference\"))\n\n            # Use adaptive image resolution\n            image_resolution = min(\n                image_resolution, adaptive_image_resolution(input_image)\n            )\n\n            print(\n                f\"🎯 Optimized settings: samples={num_samples}, resolution={image_resolution}\"\n            )\n\n            ref_flag = True\n            input_image_ori = input_image\n\n            # Check if grayscale\n            if is_gray_scale(input_image):\n                print(\"📸 Detected grayscale image\")\n            else:\n                print(\"🎨 Detected color image, converting to grayscale\")\n                input_image_ori = input_image\n                input_image = cv2.cvtColor(input_image, cv2.COLOR_RGB2LAB)[:, :, 0]\n                input_image = cv2.merge([input_image, input_image, input_image])\n\n            # Get mask\n            mask = get_mask(input_image_ori, hint_image)\n            cv2.imwrite(\"gradio_mask1_rtx3050.png\", mask)\n\n            # Handle iterative editing\n            if iterative_editing:\n                mask = 255 - mask\n                if change_according_to_strokes:\n                    kernel = np.ones((15, 15), np.uint8)\n                    mask = cv2.morphologyEx(mask, cv2.MORPH_GRADIENT, kernel)\n                    hint_image = (\n                        mask / 255.0 * hint_image + (1 - mask / 255.0) * hint_image\n                    )\n                else:\n                    hint_image = (\n                        mask / 255.0 * input_image\n                        + (1 - mask / 255.0) * input_image_ori\n                    )\n            else:\n                hint_image = (\n                    mask / 255.0 * input_image + (1 - mask / 255.0) * hint_image\n                )\n\n            hint_image = hint_image.astype(np.uint8)\n\n            # Generate prompt if empty using BLIP\n            if len(prompt) == 0 and BLIP_model is not None:\n                print(\"🤖 Generating prompt with BLIP...\")\n                with RTX3050AutocastManager():\n                    image = Image.fromarray(input_image)\n                    image = vis_processors[\"eval\"](image).unsqueeze(0).to(DEVICE)\n                    prompt = BLIP_model.generate({\"image\": image})[0]\n\n                    # Clean up prompt\n                    if (\n                        \"a black and white photo of\" in prompt\n                        or \"black and white photograph of\" in prompt\n                    ):\n                        prompt = prompt.replace(prompt[: prompt.find(\"of\") + 3], \"\")\n\n            print(f\"📝 Using prompt: {prompt}\")\n\n            # Get original dimensions\n            H_ori, W_ori, C_ori = input_image.shape\n\n            # Resize images\n            img = resize_image(input_image, image_resolution)\n            mask = resize_image(mask, image_resolution)\n            hint_image = resize_image(hint_image, image_resolution)\n\n            # Prepare mask and masked image\n            mask, masked_image = prepare_mask_and_masked_image(\n                Image.fromarray(hint_image), Image.fromarray(mask)\n            )\n            mask, masked_image_latents = encode_mask_rtx3050(mask, masked_image)\n\n            H, W, C = img.shape\n\n            # Create reference image (dummy for now)\n            ref_image = np.array([[[0] * C] * W] * H).astype(np.float32)\n            ref_image = resize_image(ref_image, image_resolution)\n\n            # Prepare control tensor\n            with RTX3050AutocastManager():\n                control = torch.from_numpy(img.copy()).float().to(DEVICE) / 255.0\n                control = torch.stack([control for _ in range(num_samples)], dim=0)\n                control = einops.rearrange(control, \"b h w c -> b c h w\").clone()\n\n            # Set seed\n            if seed == -1:\n                seed = random.randint(0, 65535)\n            seed_everything(seed)\n\n            ref_image = cv2.resize(ref_image, (W, H))\n            ref_image = torch.from_numpy(ref_image).to(DEVICE).unsqueeze(0)\n\n            init_latents = None\n\n            # Memory management for diffusion\n            if config.save_memory:\n                model.low_vram_shift(is_diffusing=False)\n\n            print(\"🎯 Preparing conditioning...\")\n\n            # Prepare conditioning with autocast\n            with RTX3050AutocastManager():\n                cond = {\n                    \"c_concat\": [control],\n                    \"c_crossattn\": [\n                        model.get_learned_conditioning(\n                            [prompt + \", \" + a_prompt] * num_samples\n                        )\n                    ],\n                }\n                un_cond = {\n                    \"c_concat\": None if guess_mode else [control],\n                    \"c_crossattn\": [\n                        model.get_learned_conditioning([n_prompt] * num_samples)\n                    ],\n                }\n\n            shape = (4, H // 8, W // 8)\n\n            # Shift to diffusion mode\n            if config.save_memory:\n                model.low_vram_shift(is_diffusing=True)\n\n            print(\"🎯 Starting diffusion sampling...\")\n\n            # Generate noise\n            noise = randn_tensor(\n                shape, generator=generator, device=DEVICE, dtype=torch.float32\n            )\n\n            # Set control scales\n            model.control_scales = (\n                [strength * (0.825 ** float(12 - i)) for i in range(13)]\n                if guess_mode\n                else ([strength] * 13)\n            )\n\n            # Monitor memory during sampling\n            memory_before = monitor_memory_usage()\n            print(f\"📊 Memory before sampling: GPU {memory_before['gpu_percent']:.1f}%\")\n\n            # Sample with RTX 3050 optimizations\n            with RTX3050AutocastManager():\n                samples, intermediates = ddim_sampler.sample(\n                    model,\n                    ddim_steps,\n                    num_samples,\n                    shape,\n                    cond,\n                    mask=mask,\n                    masked_image_latents=masked_image_latents,\n                    verbose=False,\n                    eta=eta,\n                    x_T=init_latents,\n                    unconditional_guidance_scale=scale,\n                    sag_scale=sag_scale,\n                    SAG_influence_step=SAG_influence_step,\n                    noise=noise,\n                    unconditional_conditioning=un_cond,\n                )\n\n            # Shift back from diffusion mode\n            if config.save_memory:\n                model.low_vram_shift(is_diffusing=False)\n\n            print(\"🎯 Decoding samples...\")\n\n            # Decode samples\n            with RTX3050AutocastManager():\n                if not using_deformable_vae:\n                    x_samples = model.decode_first_stage(samples)\n                else:\n                    samples = model.decode_first_stage_before_vae(samples)\n                    gray_content_z = vae_model.get_gray_content_z(\n                        torch.from_numpy(img.copy()).float().to(DEVICE) / 255.0\n                    )\n                    x_samples = vae_model.decode(samples, gray_content_z)\n\n            # Convert to numpy\n            x_samples = (\n                (einops.rearrange(x_samples, \"b c h w -> b h w c\") * 127.5 + 127.5)\n                .cpu()\n                .numpy()\n                .clip(0, 255)\n                .astype(np.uint8)\n            )\n\n            # Process results\n            results_ori = [x_samples[i] for i in range(num_samples)]\n            results_ori = [\n                cv2.resize(i, (W_ori, H_ori), interpolation=cv2.INTER_LANCZOS4)\n                for i in results_ori\n            ]\n\n            cv2.imwrite(\n                \"result_ori_rtx3050.png\",\n                cv2.cvtColor(results_ori[0], cv2.COLOR_RGB2BGR),\n            )\n\n            # Merge L channel\n            results_tmp = [\n                cv2.cvtColor(np.array(i), cv2.COLOR_RGB2LAB) for i in results_ori\n            ]\n            results = [\n                cv2.merge([input_image[:, :, 0], tmp[:, :, 1], tmp[:, :, 2]])\n                for tmp in results_tmp\n            ]\n            results_mergeL = [\n                cv2.cvtColor(np.asarray(i), cv2.COLOR_LAB2RGB) for i in results\n            ]\n\n            cv2.imwrite(\n                \"output_rtx3050.png\", cv2.cvtColor(results_mergeL[0], cv2.COLOR_RGB2BGR)\n            )\n\n            # Final memory check\n            memory_end = monitor_memory_usage()\n            print(\n                f\"📊 Final memory: GPU {memory_end['gpu_percent']:.1f}%, RAM {memory_end['ram_percent']:.1f}%\"\n            )\n            print(\"✅ RTX 3050 optimized processing complete!\")\n\n            return results_mergeL\n\n\ndef get_grayscale_img_rtx3050(img, progress=gr.Progress(track_tqdm=True)):\n    \"\"\"RTX 3050 optimized grayscale image processing\"\"\"\n    clear_gpu_cache()\n    for j in tqdm.tqdm(range(1), desc=\"Uploading input...\"):\n        return img, \"Uploading input image done.\"\n\n\n# ============================================================================\n# RTX 3050 OPTIMIZED GRADIO INTERFACE\n# ============================================================================\n\n\ndef create_rtx3050_interface():\n    \"\"\"Create RTX 3050 optimized Gradio interface\"\"\"\n\n    print(\"🎯 Creating RTX 3050 optimized interface...\")\n    device_info = get_device_info()\n\n    block = gr.Blocks().queue()\n    with block:\n        with gr.Row():\n            gr.Markdown(\"## CtrlColor - RTX 3050 Optimized\")\n            gr.Markdown(\n                f\"**Device:** {device_info['name']} ({device_info['total_memory_gb']:.1f}GB)\"\n            )\n            gr.Markdown(\n                f\"**Optimizations:** FP16: {device_info['fp16_enabled']}, Memory Fraction: {device_info['memory_fraction']}\"\n            )\n\n        with gr.Row():\n            with gr.Column():\n                grayscale_img = gr.Image(visible=False, type=\"numpy\")\n                input_image = gr.Image(\n                    source=\"upload\", tool=\"color-sketch\", interactive=True\n                )\n                Grayscale_button = gr.Button(value=\"Upload input image\")\n                text_out = gr.Textbox(\n                    value=\"RTX 3050 optimized interface ready! Upload image and draw strokes or input text prompts.\"\n                )\n\n                prompt = gr.Textbox(label=\"Prompt\")\n                change_according_to_strokes = gr.Checkbox(\n                    label=\"Change according to strokes' color\", value=True\n                )\n                iterative_editing = gr.Checkbox(\n                    label=\"Only change the strokes' area\", value=False\n                )\n                using_deformable_vae = gr.Checkbox(\n                    label=\"Using deformable vae (Less color overflow)\", value=False\n                )\n\n                with gr.Accordion(\"RTX 3050 Optimized Settings\", open=True):\n                    gr.Markdown(\"**Optimized for 4.3GB VRAM**\")\n                    num_samples = gr.Slider(\n                        1,\n                        2,\n                        value=1,\n                        step=1,\n                        label=\"Number of samples (RTX 3050 optimized)\",\n                    )\n                    image_resolution = gr.Slider(\n                        256, 768, value=512, step=64, label=\"Image Resolution\"\n                    )\n                    ddim_steps = gr.Slider(1, 50, value=20, step=1, label=\"DDIM Steps\")\n                    strength = gr.Slider(\n                        0.0, 2.0, value=1.0, step=0.01, label=\"Control Strength\"\n                    )\n                    guess_mode = gr.Checkbox(label=\"Guess Mode\", value=False)\n                    scale = gr.Slider(\n                        0.1, 30.0, value=9.0, step=0.1, label=\"Guidance Scale\"\n                    )\n                    sag_scale = gr.Slider(\n                        0.0, 1.0, value=0.05, step=0.01, label=\"SAG Scale\"\n                    )\n                    SAG_influence_step = gr.Slider(\n                        0, 1000, value=600, step=1, label=\"SAG Influence Step\"\n                    )\n                    seed = gr.Slider(\n                        -1, 2147483647, step=1, randomize=True, label=\"Seed\"\n                    )\n                    eta = gr.Number(label=\"eta (DDIM)\", value=0.0)\n\n                a_prompt = gr.Textbox(\n                    label=\"Added Prompt\", value=\"best quality, extremely detailed\"\n                )\n                n_prompt = gr.Textbox(\n                    label=\"Negative Prompt\",\n                    value=\"longbody, lowres, bad anatomy, bad hands, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality\",\n                )\n\n                run_button = gr.Button(\n                    label=\"Run (RTX 3050 Optimized)\", variant=\"primary\"\n                )\n\n            with gr.Column():\n                result_gallery = gr.Gallery(\n                    label=\"Output\", show_label=False, elem_id=\"gallery\"\n                ).style(grid=2, height=\"auto\")\n\n                # Memory monitoring display\n                with gr.Accordion(\"Memory Monitoring\", open=False):\n                    memory_display = gr.JSON(label=\"Memory Usage\")\n                    refresh_memory = gr.Button(\"Refresh Memory Info\")\n\n        # Event handlers\n        Grayscale_button.click(\n            fn=get_grayscale_img_rtx3050,\n            inputs=[input_image],\n            outputs=[grayscale_img, text_out],\n        )\n\n        run_button.click(\n            fn=process_rtx3050,\n            inputs=[\n                using_deformable_vae,\n                change_according_to_strokes,\n                iterative_editing,\n                input_image,\n                input_image,\n                prompt,\n                a_prompt,\n                n_prompt,\n                num_samples,\n                image_resolution,\n                ddim_steps,\n                guess_mode,\n                strength,\n                scale,\n                sag_scale,\n                SAG_influence_step,\n                seed,\n                eta,\n            ],\n            outputs=[result_gallery],\n        )\n\n        refresh_memory.click(fn=monitor_memory_usage, outputs=[memory_display])\n\n    return block\n\n\n# ============================================================================\n# MAIN EXECUTION\n# ============================================================================\n\nif __name__ == \"__main__\":\n    print(\"🚀 Launching RTX 3050 optimized CtrlColor interface...\")\n\n    # Create and launch interface\n    interface = create_rtx3050_interface()\n\n    # Launch with RTX 3050 optimized settings\n    interface.launch(\n        server_port=7860,\n        share=False,\n        inbrowser=True,\n        server_name=\"0.0.0.0\",  # Allow external access\n    )\n", "modifiedCode": "\"\"\"\nRTX 3050 Optimized Test Script for CtrlColor\n\nThis is a modified copy of the original test.py with RTX 3050 specific optimizations:\n- FP16 mixed precision\n- Memory management\n- Optimal batch sizes\n- GPU memory monitoring\n- Automatic fallback strategies\n\nBased on: clone/newCtrlColor/test.py\nOptimized for: NVIDIA GeForce RTX 3050 Laptop GPU (4.3GB VRAM)\n\"\"\"\n\nimport random\nimport sys\n\nimport cv2\nimport einops\nimport gradio as gr\nimport numpy as np\nimport psutil\nimport torch\nimport tqdm\nfrom PIL import Image\nfrom pytorch_lightning import seed_everything\n\n# Import RTX 3050 optimized config\nfrom .config_rtx3050 import (\n    DEFAULT_IMAGE_RESOLUTION,\n    DEVICE,\n    USE_FP16,\n    RTX3050AutocastManager,\n    RTX3050MemoryManager,\n    clear_gpu_cache,\n    get_device_info,\n    get_optimal_batch_size,\n    get_optimal_image_resolution,\n)\n\n# Original imports (preserved)\nsys.path.append(\"..\")\nfrom lavis.models import load_model_and_preprocess\n\nimport config\nfrom annotator.util import resize_image\nfrom cldm.ddim_haced_sag_step import DDIMSampler\nfrom cldm.model import create_model, load_state_dict\nfrom ldm.models.autoencoder_train import AutoencoderKL\nfrom share import *\n\n# ============================================================================\n# RTX 3050 OPTIMIZED MODEL LOADING\n# ============================================================================\n\n\ndef load_model_rtx3050():\n    \"\"\"Load CtrlColor model with RTX 3050 optimizations\"\"\"\n    print(\"🎯 Loading CtrlColor model with RTX 3050 optimizations...\")\n\n    # Model paths\n    ckpt_path = \"./pretrained_models/main_model.ckpt\"\n    config_path = \"./models/cldm_v15_inpainting_infer1.yaml\"\n\n    with RTX3050MemoryManager():\n        # Load model on CPU first to save VRAM\n        model = create_model(config_path).cpu()\n\n        # Load state dict with memory optimization\n        state_dict = load_state_dict(ckpt_path, location=\"cpu\")\n        model.load_state_dict(state_dict, strict=False)\n\n        # Move to GPU with FP16 if enabled\n        model = model.to(DEVICE)\n        if USE_FP16:\n            model = model.half()\n\n        print(f\"✅ Model loaded on {DEVICE} with FP16: {USE_FP16}\")\n\n        return model\n\n\ndef load_vae_rtx3050():\n    \"\"\"Load VAE model with RTX 3050 optimizations\"\"\"\n    print(\"🎯 Loading VAE model with RTX 3050 optimizations...\")\n\n    vae_model_ckpt_path = \"./pretrained_models/content-guided_deformable_vae.ckpt\"\n\n    init_config = {\n        \"embed_dim\": 4,\n        \"monitor\": \"val/rec_loss\",\n        \"ddconfig\": {\n            \"double_z\": True,\n            \"z_channels\": 4,\n            \"resolution\": 256,\n            \"in_channels\": 3,\n            \"out_ch\": 3,\n            \"ch\": 128,\n            \"ch_mult\": [1, 2, 4, 4],\n            \"num_res_blocks\": 2,\n            \"attn_resolutions\": [],\n            \"dropout\": 0.0,\n        },\n        \"lossconfig\": {\n            \"target\": \"ldm.modules.losses.LPIPSWithDiscriminator\",\n            \"params\": {\n                \"disc_start\": 501,\n                \"kl_weight\": 0,\n                \"disc_weight\": 0.025,\n                \"disc_factor\": 1.0,\n            },\n        },\n    }\n\n    with RTX3050MemoryManager():\n        vae = AutoencoderKL(**init_config)\n        vae.load_state_dict(load_state_dict(vae_model_ckpt_path, location=\"cpu\"))\n        vae = vae.to(DEVICE)\n\n        if USE_FP16:\n            vae = vae.half()\n\n        print(f\"✅ VAE loaded on {DEVICE} with FP16: {USE_FP16}\")\n\n        return vae\n\n\ndef load_blip_rtx3050():\n    \"\"\"Load BLIP model with RTX 3050 optimizations\"\"\"\n    print(\"🎯 Loading BLIP model with RTX 3050 optimizations...\")\n\n    try:\n        BLIP_model, vis_processors, _ = load_model_and_preprocess(\n            name=\"blip_caption\", model_type=\"base_coco\", is_eval=True, device=DEVICE\n        )\n\n        if USE_FP16:\n            BLIP_model = BLIP_model.half()\n\n        print(\"✅ BLIP model loaded successfully\")\n        return BLIP_model, vis_processors\n\n    except Exception as e:\n        print(f\"⚠️ BLIP loading failed: {e}\")\n        return None, None\n\n\n# ============================================================================\n# INITIALIZE MODELS WITH RTX 3050 OPTIMIZATIONS\n# ============================================================================\n\nprint(\"🚀 Initializing CtrlColor with RTX 3050 optimizations...\")\nprint(f\"📊 Device info: {get_device_info()}\")\n\n# Load models with optimizations\nmodel = load_model_rtx3050()\nddim_sampler = DDIMSampler(model)\nvae_model = load_vae_rtx3050()\nBLIP_model, vis_processors = load_blip_rtx3050()\n\n# Set generator seed\ngenerator = torch.manual_seed(859311133)\n\n# ============================================================================\n# RTX 3050 OPTIMIZED UTILITY FUNCTIONS\n# ============================================================================\n\n\ndef monitor_memory_usage():\n    \"\"\"Monitor GPU and RAM memory usage\"\"\"\n    if torch.cuda.is_available():\n        gpu_memory = torch.cuda.memory_allocated() / 1024**3\n        gpu_total = torch.cuda.get_device_properties(0).total_memory / 1024**3\n        gpu_percent = (gpu_memory / gpu_total) * 100\n    else:\n        gpu_memory = gpu_total = gpu_percent = 0\n\n    ram_percent = psutil.virtual_memory().percent\n\n    return {\n        \"gpu_memory_gb\": gpu_memory,\n        \"gpu_total_gb\": gpu_total,\n        \"gpu_percent\": gpu_percent,\n        \"ram_percent\": ram_percent,\n    }\n\n\ndef adaptive_image_resolution(input_image, conservative=True):\n    \"\"\"Adaptively choose image resolution based on memory\"\"\"\n    memory_info = monitor_memory_usage()\n\n    # If GPU memory usage is high, use conservative resolution\n    if memory_info[\"gpu_percent\"] > 70 or memory_info[\"ram_percent\"] > 85:\n        return 256  # Very conservative\n    elif conservative:\n        return DEFAULT_IMAGE_RESOLUTION  # 512\n    else:\n        return get_optimal_image_resolution(conservative=False)  # 768\n\n\ndef encode_mask_rtx3050(mask, masked_image):\n    \"\"\"RTX 3050 optimized mask encoding\"\"\"\n    with RTX3050AutocastManager():\n        mask = torch.nn.functional.interpolate(\n            mask, size=(mask.shape[2] // 8, mask.shape[3] // 8)\n        )\n        mask = mask.to(device=DEVICE)\n\n        # Use autocast for VAE encoding\n        with torch.cuda.amp.autocast(enabled=USE_FP16):\n            masked_image_latents = model.get_first_stage_encoding(\n                model.encode_first_stage(masked_image.to(DEVICE))\n            ).detach()\n\n        return mask, masked_image_latents\n\n\n# ============================================================================\n# PRESERVED ORIGINAL FUNCTIONS (with minor optimizations)\n# ============================================================================\n\n\ndef get_mask(input_image, hint_image):\n    \"\"\"Get mask from input and hint images (preserved from original)\"\"\"\n    mask = input_image.copy()\n    H, W, C = input_image.shape\n\n    for i in range(H):\n        for j in range(W):\n            if input_image[i, j, 0] == hint_image[i, j, 0]:\n                mask[i, j, :] = 255.0\n            else:\n                mask[i, j, :] = 0.0\n\n    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))\n    mask = cv2.morphologyEx(np.array(mask), cv2.MORPH_OPEN, kernel, iterations=1)\n    return mask\n\n\ndef prepare_mask_and_masked_image(image, mask):\n    \"\"\"Prepare mask and masked image (preserved from original)\"\"\"\n    # [Original function implementation preserved]\n    # This is a complex function, keeping original logic\n\n    if isinstance(image, torch.Tensor):\n        if not isinstance(mask, torch.Tensor):\n            raise TypeError(\n                f\"`image` is a torch.Tensor but `mask` (type: {type(mask)} is not\"\n            )\n\n        # Batch single image\n        if image.ndim == 3:\n            assert image.shape[0] == 3, (\n                \"Image outside a batch should be of shape (3, H, W)\"\n            )\n            image = image.unsqueeze(0)\n\n        # Batch and add channel dim for single mask\n        if mask.ndim == 2:\n            mask = mask.unsqueeze(0).unsqueeze(0)\n\n        # Batch single mask or add channel dim\n        if mask.ndim == 3:\n            if mask.shape[0] == 1:\n                mask = mask.unsqueeze(0)\n            else:\n                mask = mask.unsqueeze(1)\n\n        assert image.ndim == 4 and mask.ndim == 4, (\n            \"Image and Mask must have 4 dimensions\"\n        )\n        assert image.shape[-2:] == mask.shape[-2:], (\n            \"Image and Mask must have the same spatial dimensions\"\n        )\n        assert image.shape[0] == mask.shape[0], (\n            \"Image and Mask must have the same batch size\"\n        )\n\n        # Check image is in [-1, 1]\n        if image.min() < -1 or image.max() > 1:\n            raise ValueError(\"Image should be in [-1, 1] range\")\n\n        # Check mask is in [0, 1]\n        if mask.min() < 0 or mask.max() > 1:\n            raise ValueError(\"Mask should be in [0, 1] range\")\n\n        # Binarize mask\n        mask[mask < 0.5] = 0\n        mask[mask >= 0.5] = 1\n\n        # Image as float32\n        image = image.to(dtype=torch.float32)\n    elif isinstance(mask, torch.Tensor):\n        raise TypeError(\n            f\"`mask` is a torch.Tensor but `image` (type: {type(image)} is not\"\n        )\n    else:\n        # preprocess image\n        if isinstance(image, (Image.Image, np.ndarray)):\n            image = [image]\n\n        if isinstance(image, list) and isinstance(image[0], Image.Image):\n            image = [np.array(i.convert(\"RGB\"))[None, :] for i in image]\n            image = np.concatenate(image, axis=0)\n        elif isinstance(image, list) and isinstance(image[0], np.ndarray):\n            image = np.concatenate([i[None, :] for i in image], axis=0)\n\n        image = image.transpose(0, 3, 1, 2)\n        image = torch.from_numpy(image).to(dtype=torch.float32) / 127.5 - 1.0\n\n        # preprocess mask\n        if isinstance(mask, (Image.Image, np.ndarray)):\n            mask = [mask]\n\n        if isinstance(mask, list) and isinstance(mask[0], Image.Image):\n            mask = np.concatenate(\n                [np.array(m.convert(\"L\"))[None, None, :] for m in mask], axis=0\n            )\n            mask = mask.astype(np.float32) / 255.0\n        elif isinstance(mask, list) and isinstance(mask[0], np.ndarray):\n            mask = np.concatenate([m[None, None, :] for m in mask], axis=0)\n\n        mask[mask < 0.5] = 0\n        mask[mask >= 0.5] = 1\n        mask = torch.from_numpy(mask)\n\n    masked_image = image * (mask < 0.5)\n    return mask, masked_image\n\n\ndef is_gray_scale(img, threshold=10):\n    \"\"\"Check if image is grayscale (preserved from original)\"\"\"\n    img = Image.fromarray(img)\n    if len(img.getbands()) == 1:\n        return True\n\n    img1 = np.asarray(img.getchannel(channel=0), dtype=np.int16)\n    img2 = np.asarray(img.getchannel(channel=1), dtype=np.int16)\n    img3 = np.asarray(img.getchannel(channel=2), dtype=np.int16)\n\n    diff1 = (img1 - img2).var()\n    diff2 = (img2 - img3).var()\n    diff3 = (img3 - img1).var()\n    diff_sum = (diff1 + diff2 + diff3) / 3.0\n\n    return diff_sum <= threshold\n\n\ndef randn_tensor(shape, generator=None, device=None, dtype=None, layout=None):\n    \"\"\"Create random tensor (preserved from original with RTX 3050 optimizations)\"\"\"\n    rand_device = device\n    batch_size = shape[0]\n\n    layout = layout or torch.strided\n    device = device or torch.device(\"cpu\")\n\n    if generator is not None:\n        gen_device_type = (\n            generator.device.type\n            if not isinstance(generator, list)\n            else generator[0].device.type\n        )\n        if gen_device_type != device.type and gen_device_type == \"cpu\":\n            rand_device = \"cpu\"\n            if device != \"mps\":\n                print(\n                    \"The passed generator was created on 'cpu' even though a tensor on {device} was expected.\"\n                )\n        elif gen_device_type != device.type and gen_device_type == \"cuda\":\n            raise ValueError(\n                f\"Cannot generate a {device} tensor from a generator of type {gen_device_type}.\"\n            )\n\n    # make sure generator list of length 1 is treated like a non-list\n    if isinstance(generator, list) and len(generator) == 1:\n        generator = generator[0]\n\n    if isinstance(generator, list):\n        shape = (1,) + shape[1:]\n        latents = [\n            torch.randn(\n                shape,\n                generator=generator[i],\n                device=rand_device,\n                dtype=dtype,\n                layout=layout,\n            )\n            for i in range(batch_size)\n        ]\n        latents = torch.cat(latents, dim=0).to(device)\n    else:\n        latents = torch.randn(\n            shape, generator=generator, device=rand_device, dtype=dtype, layout=layout\n        ).to(device)\n\n    return latents\n\n\n# ============================================================================\n# RTX 3050 OPTIMIZED MAIN PROCESSING FUNCTION\n# ============================================================================\n\n\ndef process_rtx3050(\n    using_deformable_vae,\n    change_according_to_strokes,\n    iterative_editing,\n    input_image,\n    hint_image,\n    prompt,\n    a_prompt,\n    n_prompt,\n    num_samples,\n    image_resolution,\n    ddim_steps,\n    guess_mode,\n    strength,\n    scale,\n    sag_scale,\n    SAG_influence_step,\n    seed,\n    eta,\n):\n    \"\"\"\n    RTX 3050 optimized main processing function\n\n    Key optimizations:\n    - Memory management with context managers\n    - FP16 mixed precision\n    - Adaptive batch sizing\n    - Memory monitoring\n    - Automatic fallback strategies\n    \"\"\"\n\n    print(\"🎯 Starting RTX 3050 optimized processing...\")\n    memory_start = monitor_memory_usage()\n    print(\n        f\"📊 Initial memory: GPU {memory_start['gpu_percent']:.1f}%, RAM {memory_start['ram_percent']:.1f}%\"\n    )\n\n    # Clear cache before processing\n    clear_gpu_cache()\n\n    with RTX3050MemoryManager(clear_cache_before=True, clear_cache_after=True):\n        with torch.no_grad():\n            # Adaptive settings based on memory\n            if memory_start[\"gpu_percent\"] > 70:\n                print(\"⚠️ High GPU memory usage detected, using conservative settings\")\n                num_samples = min(num_samples, 1)\n                image_resolution = min(image_resolution, 256)\n\n            # Use optimal batch size\n            num_samples = min(num_samples, get_optimal_batch_size(\"inference\"))\n\n            # Use adaptive image resolution\n            image_resolution = min(\n                image_resolution, adaptive_image_resolution(input_image)\n            )\n\n            print(\n                f\"🎯 Optimized settings: samples={num_samples}, resolution={image_resolution}\"\n            )\n\n            ref_flag = True\n            input_image_ori = input_image\n\n            # Check if grayscale\n            if is_gray_scale(input_image):\n                print(\"📸 Detected grayscale image\")\n            else:\n                print(\"🎨 Detected color image, converting to grayscale\")\n                input_image_ori = input_image\n                input_image = cv2.cvtColor(input_image, cv2.COLOR_RGB2LAB)[:, :, 0]\n                input_image = cv2.merge([input_image, input_image, input_image])\n\n            # Get mask\n            mask = get_mask(input_image_ori, hint_image)\n            cv2.imwrite(\"gradio_mask1_rtx3050.png\", mask)\n\n            # Handle iterative editing\n            if iterative_editing:\n                mask = 255 - mask\n                if change_according_to_strokes:\n                    kernel = np.ones((15, 15), np.uint8)\n                    mask = cv2.morphologyEx(mask, cv2.MORPH_GRADIENT, kernel)\n                    hint_image = (\n                        mask / 255.0 * hint_image + (1 - mask / 255.0) * hint_image\n                    )\n                else:\n                    hint_image = (\n                        mask / 255.0 * input_image\n                        + (1 - mask / 255.0) * input_image_ori\n                    )\n            else:\n                hint_image = (\n                    mask / 255.0 * input_image + (1 - mask / 255.0) * hint_image\n                )\n\n            hint_image = hint_image.astype(np.uint8)\n\n            # Generate prompt if empty using BLIP\n            if len(prompt) == 0 and BLIP_model is not None:\n                print(\"🤖 Generating prompt with BLIP...\")\n                with RTX3050AutocastManager():\n                    image = Image.fromarray(input_image)\n                    image = vis_processors[\"eval\"](image).unsqueeze(0).to(DEVICE)\n                    prompt = BLIP_model.generate({\"image\": image})[0]\n\n                    # Clean up prompt\n                    if (\n                        \"a black and white photo of\" in prompt\n                        or \"black and white photograph of\" in prompt\n                    ):\n                        prompt = prompt.replace(prompt[: prompt.find(\"of\") + 3], \"\")\n\n            print(f\"📝 Using prompt: {prompt}\")\n\n            # Get original dimensions\n            H_ori, W_ori, C_ori = input_image.shape\n\n            # Resize images\n            img = resize_image(input_image, image_resolution)\n            mask = resize_image(mask, image_resolution)\n            hint_image = resize_image(hint_image, image_resolution)\n\n            # Prepare mask and masked image\n            mask, masked_image = prepare_mask_and_masked_image(\n                Image.fromarray(hint_image), Image.fromarray(mask)\n            )\n            mask, masked_image_latents = encode_mask_rtx3050(mask, masked_image)\n\n            H, W, C = img.shape\n\n            # Create reference image (dummy for now)\n            ref_image = np.array([[[0] * C] * W] * H).astype(np.float32)\n            ref_image = resize_image(ref_image, image_resolution)\n\n            # Prepare control tensor\n            with RTX3050AutocastManager():\n                control = torch.from_numpy(img.copy()).float().to(DEVICE) / 255.0\n                control = torch.stack([control for _ in range(num_samples)], dim=0)\n                control = einops.rearrange(control, \"b h w c -> b c h w\").clone()\n\n            # Set seed\n            if seed == -1:\n                seed = random.randint(0, 65535)\n            seed_everything(seed)\n\n            ref_image = cv2.resize(ref_image, (W, H))\n            ref_image = torch.from_numpy(ref_image).to(DEVICE).unsqueeze(0)\n\n            init_latents = None\n\n            # Memory management for diffusion\n            if config.save_memory:\n                model.low_vram_shift(is_diffusing=False)\n\n            print(\"🎯 Preparing conditioning...\")\n\n            # Prepare conditioning with autocast\n            with RTX3050AutocastManager():\n                cond = {\n                    \"c_concat\": [control],\n                    \"c_crossattn\": [\n                        model.get_learned_conditioning(\n                            [prompt + \", \" + a_prompt] * num_samples\n                        )\n                    ],\n                }\n                un_cond = {\n                    \"c_concat\": None if guess_mode else [control],\n                    \"c_crossattn\": [\n                        model.get_learned_conditioning([n_prompt] * num_samples)\n                    ],\n                }\n\n            shape = (4, H // 8, W // 8)\n\n            # Shift to diffusion mode\n            if config.save_memory:\n                model.low_vram_shift(is_diffusing=True)\n\n            print(\"🎯 Starting diffusion sampling...\")\n\n            # Generate noise\n            noise = randn_tensor(\n                shape, generator=generator, device=DEVICE, dtype=torch.float32\n            )\n\n            # Set control scales\n            model.control_scales = (\n                [strength * (0.825 ** float(12 - i)) for i in range(13)]\n                if guess_mode\n                else ([strength] * 13)\n            )\n\n            # Monitor memory during sampling\n            memory_before = monitor_memory_usage()\n            print(f\"📊 Memory before sampling: GPU {memory_before['gpu_percent']:.1f}%\")\n\n            # Sample with RTX 3050 optimizations\n            with RTX3050AutocastManager():\n                samples, intermediates = ddim_sampler.sample(\n                    model,\n                    ddim_steps,\n                    num_samples,\n                    shape,\n                    cond,\n                    mask=mask,\n                    masked_image_latents=masked_image_latents,\n                    verbose=False,\n                    eta=eta,\n                    x_T=init_latents,\n                    unconditional_guidance_scale=scale,\n                    sag_scale=sag_scale,\n                    SAG_influence_step=SAG_influence_step,\n                    noise=noise,\n                    unconditional_conditioning=un_cond,\n                )\n\n            # Shift back from diffusion mode\n            if config.save_memory:\n                model.low_vram_shift(is_diffusing=False)\n\n            print(\"🎯 Decoding samples...\")\n\n            # Decode samples\n            with RTX3050AutocastManager():\n                if not using_deformable_vae:\n                    x_samples = model.decode_first_stage(samples)\n                else:\n                    samples = model.decode_first_stage_before_vae(samples)\n                    gray_content_z = vae_model.get_gray_content_z(\n                        torch.from_numpy(img.copy()).float().to(DEVICE) / 255.0\n                    )\n                    x_samples = vae_model.decode(samples, gray_content_z)\n\n            # Convert to numpy\n            x_samples = (\n                (einops.rearrange(x_samples, \"b c h w -> b h w c\") * 127.5 + 127.5)\n                .cpu()\n                .numpy()\n                .clip(0, 255)\n                .astype(np.uint8)\n            )\n\n            # Process results\n            results_ori = [x_samples[i] for i in range(num_samples)]\n            results_ori = [\n                cv2.resize(i, (W_ori, H_ori), interpolation=cv2.INTER_LANCZOS4)\n                for i in results_ori\n            ]\n\n            cv2.imwrite(\n                \"result_ori_rtx3050.png\",\n                cv2.cvtColor(results_ori[0], cv2.COLOR_RGB2BGR),\n            )\n\n            # Merge L channel\n            results_tmp = [\n                cv2.cvtColor(np.array(i), cv2.COLOR_RGB2LAB) for i in results_ori\n            ]\n            results = [\n                cv2.merge([input_image[:, :, 0], tmp[:, :, 1], tmp[:, :, 2]])\n                for tmp in results_tmp\n            ]\n            results_mergeL = [\n                cv2.cvtColor(np.asarray(i), cv2.COLOR_LAB2RGB) for i in results\n            ]\n\n            cv2.imwrite(\n                \"output_rtx3050.png\", cv2.cvtColor(results_mergeL[0], cv2.COLOR_RGB2BGR)\n            )\n\n            # Final memory check\n            memory_end = monitor_memory_usage()\n            print(\n                f\"📊 Final memory: GPU {memory_end['gpu_percent']:.1f}%, RAM {memory_end['ram_percent']:.1f}%\"\n            )\n            print(\"✅ RTX 3050 optimized processing complete!\")\n\n            return results_mergeL\n\n\ndef get_grayscale_img_rtx3050(img, progress=gr.Progress(track_tqdm=True)):\n    \"\"\"RTX 3050 optimized grayscale image processing\"\"\"\n    clear_gpu_cache()\n    for j in tqdm.tqdm(range(1), desc=\"Uploading input...\"):\n        return img, \"Uploading input image done.\"\n\n\n# ============================================================================\n# RTX 3050 OPTIMIZED GRADIO INTERFACE\n# ============================================================================\n\n\ndef create_rtx3050_interface():\n    \"\"\"Create RTX 3050 optimized Gradio interface\"\"\"\n\n    print(\"🎯 Creating RTX 3050 optimized interface...\")\n    device_info = get_device_info()\n\n    block = gr.Blocks().queue()\n    with block:\n        with gr.Row():\n            gr.Markdown(\"## CtrlColor - RTX 3050 Optimized\")\n            gr.Markdown(\n                f\"**Device:** {device_info['name']} ({device_info['total_memory_gb']:.1f}GB)\"\n            )\n            gr.Markdown(\n                f\"**Optimizations:** FP16: {device_info['fp16_enabled']}, Memory Fraction: {device_info['memory_fraction']}\"\n            )\n\n        with gr.Row():\n            with gr.Column():\n                grayscale_img = gr.Image(visible=False, type=\"numpy\")\n                input_image = gr.Image(\n                    source=\"upload\", tool=\"color-sketch\", interactive=True\n                )\n                Grayscale_button = gr.Button(value=\"Upload input image\")\n                text_out = gr.Textbox(\n                    value=\"RTX 3050 optimized interface ready! Upload image and draw strokes or input text prompts.\"\n                )\n\n                prompt = gr.Textbox(label=\"Prompt\")\n                change_according_to_strokes = gr.Checkbox(\n                    label=\"Change according to strokes' color\", value=True\n                )\n                iterative_editing = gr.Checkbox(\n                    label=\"Only change the strokes' area\", value=False\n                )\n                using_deformable_vae = gr.Checkbox(\n                    label=\"Using deformable vae (Less color overflow)\", value=False\n                )\n\n                with gr.Accordion(\"RTX 3050 Optimized Settings\", open=True):\n                    gr.Markdown(\"**Optimized for 4.3GB VRAM**\")\n                    num_samples = gr.Slider(\n                        1,\n                        2,\n                        value=1,\n                        step=1,\n                        label=\"Number of samples (RTX 3050 optimized)\",\n                    )\n                    image_resolution = gr.Slider(\n                        256, 768, value=512, step=64, label=\"Image Resolution\"\n                    )\n                    ddim_steps = gr.Slider(1, 50, value=20, step=1, label=\"DDIM Steps\")\n                    strength = gr.Slider(\n                        0.0, 2.0, value=1.0, step=0.01, label=\"Control Strength\"\n                    )\n                    guess_mode = gr.Checkbox(label=\"Guess Mode\", value=False)\n                    scale = gr.Slider(\n                        0.1, 30.0, value=9.0, step=0.1, label=\"Guidance Scale\"\n                    )\n                    sag_scale = gr.Slider(\n                        0.0, 1.0, value=0.05, step=0.01, label=\"SAG Scale\"\n                    )\n                    SAG_influence_step = gr.Slider(\n                        0, 1000, value=600, step=1, label=\"SAG Influence Step\"\n                    )\n                    seed = gr.Slider(\n                        -1, 2147483647, step=1, randomize=True, label=\"Seed\"\n                    )\n                    eta = gr.Number(label=\"eta (DDIM)\", value=0.0)\n\n                a_prompt = gr.Textbox(\n                    label=\"Added Prompt\", value=\"best quality, extremely detailed\"\n                )\n                n_prompt = gr.Textbox(\n                    label=\"Negative Prompt\",\n                    value=\"longbody, lowres, bad anatomy, bad hands, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality\",\n                )\n\n                run_button = gr.Button(\n                    label=\"Run (RTX 3050 Optimized)\", variant=\"primary\"\n                )\n\n            with gr.Column():\n                result_gallery = gr.Gallery(\n                    label=\"Output\", show_label=False, elem_id=\"gallery\"\n                ).style(grid=2, height=\"auto\")\n\n                # Memory monitoring display\n                with gr.Accordion(\"Memory Monitoring\", open=False):\n                    memory_display = gr.JSON(label=\"Memory Usage\")\n                    refresh_memory = gr.Button(\"Refresh Memory Info\")\n\n        # Event handlers\n        Grayscale_button.click(\n            fn=get_grayscale_img_rtx3050,\n            inputs=[input_image],\n            outputs=[grayscale_img, text_out],\n        )\n\n        run_button.click(\n            fn=process_rtx3050,\n            inputs=[\n                using_deformable_vae,\n                change_according_to_strokes,\n                iterative_editing,\n                input_image,\n                input_image,\n                prompt,\n                a_prompt,\n                n_prompt,\n                num_samples,\n                image_resolution,\n                ddim_steps,\n                guess_mode,\n                strength,\n                scale,\n                sag_scale,\n                SAG_influence_step,\n                seed,\n                eta,\n            ],\n            outputs=[result_gallery],\n        )\n\n        refresh_memory.click(fn=monitor_memory_usage, outputs=[memory_display])\n\n    return block\n\n\n# ============================================================================\n# MAIN EXECUTION\n# ============================================================================\n\nif __name__ == \"__main__\":\n    print(\"🚀 Launching RTX 3050 optimized CtrlColor interface...\")\n\n    # Create and launch interface\n    interface = create_rtx3050_interface()\n\n    # Launch with RTX 3050 optimized settings\n    interface.launch(\n        server_port=7860,\n        share=False,\n        inbrowser=True,\n        server_name=\"0.0.0.0\",  # Allow external access\n    )\n"}
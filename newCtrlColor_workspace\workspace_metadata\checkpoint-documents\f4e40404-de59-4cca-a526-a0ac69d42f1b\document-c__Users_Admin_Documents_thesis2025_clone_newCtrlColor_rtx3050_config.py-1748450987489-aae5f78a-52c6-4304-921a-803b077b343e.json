{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\newCtrlColor\\rtx3050_config.py"}, "originalCode": "\"\"\"\nEnhanced RTX 3050 Optimization Settings\n\nBased on CUDA OOM analysis from test_rtx3050_simple.log, this provides\ncomprehensive memory management for RTX 3050 (4GB VRAM).\n\nKey fixes for the OOM issue:\n- CPU-first model loading strategy\n- Memory-efficient checkpoint loading\n- Dynamic memory monitoring\n- Automatic fallback to CPU when needed\n\nOptimizations:\n- FP16 mixed precision (50% memory savings)\n- Memory management (85% VRAM usage)\n- Optimal batch sizes\n- Automatic cache clearing\n- CPU fallback strategies\n\"\"\"\n\nimport gc\nimport os\nfrom contextlib import contextmanager\n\nimport torch\n\n# ============================================================================\n# RTX 3050 OPTIMIZATIONS (ENHANCED)\n# ============================================================================\n\n\nclass MemoryManager:\n    \"\"\"Enhanced memory manager for RTX 3050\"\"\"\n\n    def __init__(self):\n        self.max_memory_gb = 3.4  # 85% of 4GB\n\n    def get_memory_info(self):\n        \"\"\"Get current GPU memory usage\"\"\"\n        if torch.cuda.is_available():\n            allocated = torch.cuda.memory_allocated() / 1024**3\n            reserved = torch.cuda.memory_reserved() / 1024**3\n            return allocated, reserved\n        return 0, 0\n\n    def check_memory_available(self, required_gb):\n        \"\"\"Check if enough memory is available\"\"\"\n        allocated, _ = self.get_memory_info()\n        available = self.max_memory_gb - allocated\n        return available >= required_gb\n\n    @contextmanager\n    def memory_efficient_loading(self):\n        \"\"\"Context manager for memory-efficient operations\"\"\"\n        clear_memory()\n        try:\n            yield\n        finally:\n            clear_memory()\n\n\n# Global memory manager instance\nmemory_manager = MemoryManager()\n\n\ndef setup_rtx3050():\n    \"\"\"Apply enhanced RTX 3050 optimizations\"\"\"\n    if torch.cuda.is_available():\n        # Set memory fraction to 85% of 4GB\n        torch.cuda.set_per_process_memory_fraction(0.85)\n\n        # Set CUDA memory allocation strategy to avoid fragmentation\n        os.environ[\"PYTORCH_CUDA_ALLOC_CONF\"] = \"max_split_size_mb:128\"\n\n        # Enable cuDNN optimizations\n        torch.backends.cudnn.benchmark = True\n        torch.backends.cudnn.enabled = True\n\n        # Enable memory efficient attention if available\n        try:\n            torch.backends.cuda.enable_flash_sdp(True)\n        except:\n            pass  # Flash attention not available\n\n        print(\"✅ Enhanced RTX 3050 optimizations applied:\")\n        print(f\"   - GPU: {torch.cuda.get_device_name(0)}\")\n        print(\n            f\"   - Memory limit: 85% ({torch.cuda.get_device_properties(0).total_memory / 1024**3 * 0.85:.1f}GB)\"\n        )\n        print(\"   - FP16: Enabled\")\n        print(\"   - Batch size: 1 (memory-safe)\")\n        print(\"   - Memory fragmentation: Reduced\")\n        print(\"   - CPU fallback: Available\")\n    else:\n        print(\"⚠️ CUDA not available, using CPU mode\")\n\n\ndef clear_memory():\n    \"\"\"Clear GPU memory\"\"\"\n    if torch.cuda.is_available():\n        torch.cuda.empty_cache()\n    gc.collect()\n\n\ndef get_optimal_settings():\n    \"\"\"Get optimal settings for RTX 3050\"\"\"\n    return {\n        \"num_samples\": 1,  # Safe batch size\n        \"image_resolution\": 256,  # Memory-safe resolution (reduced from 512)\n        \"ddim_steps\": 10,  # Reduced steps for memory efficiency\n        \"use_fp16\": True,  # Enable FP16\n        \"clear_cache\": True,  # Clear cache between runs\n        \"cpu_fallback\": True,  # Enable CPU fallback\n        \"load_on_cpu_first\": True,  # Load model on CPU first\n    }\n\n\ndef load_model_memory_safe(create_model_func, config_path, ckpt_path):\n    \"\"\"\n    Memory-safe model loading for RTX 3050\n\n    This addresses the CUDA OOM issue from test_rtx3050_simple.log\n    \"\"\"\n    print(\"🔧 Loading model with memory-safe strategy...\")\n\n    with memory_manager.memory_efficient_loading():\n        try:\n            # Step 1: Create model on CPU\n            print(\"   - Creating model on CPU...\")\n            model = create_model_func(config_path).cpu()\n\n            # Step 2: Load checkpoint to CPU\n            print(\"   - Loading checkpoint to CPU...\")\n            checkpoint = torch.load(ckpt_path, map_location=\"cpu\")\n\n            # Step 3: Load state dict on CPU\n            print(\"   - Loading state dict on CPU...\")\n            if hasattr(model, \"load_state_dict\"):\n                model.load_state_dict(checkpoint, strict=False)\n\n            # Clean up checkpoint\n            del checkpoint\n            clear_memory()\n\n            # Step 4: Try GPU transfer\n            if torch.cuda.is_available():\n                print(\"   - Attempting GPU transfer...\")\n\n                # Estimate model size\n                param_count = sum(p.numel() for p in model.parameters())\n                model_size_gb = param_count * 4 / 1024**3\n\n                if memory_manager.check_memory_available(model_size_gb):\n                    model = model.cuda().half()  # Move to GPU and convert to FP16\n                    print(\"   ✅ Model loaded on GPU with FP16\")\n                else:\n                    print(\"   ⚠️ Insufficient GPU memory, keeping on CPU\")\n\n            return model\n\n        except Exception as e:\n            print(f\"   ❌ Model loading failed: {e}\")\n            return None\n\n\n# Auto-apply optimizations when imported\nsetup_rtx3050()\n\n# Export key functions\n__all__ = [\n    \"setup_rtx3050\",\n    \"clear_memory\",\n    \"get_optimal_settings\",\n    \"load_model_memory_safe\",\n    \"memory_manager\",\n]\n", "modifiedCode": "\"\"\"\nEnhanced RTX 3050 Optimization Settings\n\nBased on CUDA OOM analysis from test_rtx3050_simple.log, this provides\ncomprehensive memory management for RTX 3050 (4GB VRAM).\n\nKey fixes for the OOM issue:\n- CPU-first model loading strategy\n- Memory-efficient checkpoint loading\n- Dynamic memory monitoring\n- Automatic fallback to CPU when needed\n\nOptimizations:\n- FP16 mixed precision (50% memory savings)\n- Memory management (85% VRAM usage)\n- Optimal batch sizes\n- Automatic cache clearing\n- CPU fallback strategies\n\"\"\"\n\nimport gc\nimport os\nfrom contextlib import contextmanager\n\nimport torch\n\n# ============================================================================\n# RTX 3050 OPTIMIZATIONS (ENHANCED)\n# ============================================================================\n\n\nclass MemoryManager:\n    \"\"\"Enhanced memory manager for RTX 3050\"\"\"\n\n    def __init__(self):\n        self.max_memory_gb = 3.4  # 85% of 4GB\n\n    def get_memory_info(self):\n        \"\"\"Get current GPU memory usage\"\"\"\n        if torch.cuda.is_available():\n            allocated = torch.cuda.memory_allocated() / 1024**3\n            reserved = torch.cuda.memory_reserved() / 1024**3\n            return allocated, reserved\n        return 0, 0\n\n    def check_memory_available(self, required_gb):\n        \"\"\"Check if enough memory is available\"\"\"\n        allocated, _ = self.get_memory_info()\n        available = self.max_memory_gb - allocated\n        return available >= required_gb\n\n    @contextmanager\n    def memory_efficient_loading(self):\n        \"\"\"Context manager for memory-efficient operations\"\"\"\n        clear_memory()\n        try:\n            yield\n        finally:\n            clear_memory()\n\n\n# Global memory manager instance\nmemory_manager = MemoryManager()\n\n\ndef setup_rtx3050():\n    \"\"\"Apply enhanced RTX 3050 optimizations\"\"\"\n    if torch.cuda.is_available():\n        # Set memory fraction to 85% of 4GB\n        torch.cuda.set_per_process_memory_fraction(0.85)\n\n        # Set CUDA memory allocation strategy to avoid fragmentation\n        os.environ[\"PYTORCH_CUDA_ALLOC_CONF\"] = \"max_split_size_mb:128\"\n\n        # Enable cuDNN optimizations\n        torch.backends.cudnn.benchmark = True\n        torch.backends.cudnn.enabled = True\n\n        # Enable memory efficient attention if available\n        try:\n            torch.backends.cuda.enable_flash_sdp(True)\n        except:\n            pass  # Flash attention not available\n\n        print(\"✅ Enhanced RTX 3050 optimizations applied:\")\n        print(f\"   - GPU: {torch.cuda.get_device_name(0)}\")\n        print(\n            f\"   - Memory limit: 85% ({torch.cuda.get_device_properties(0).total_memory / 1024**3 * 0.85:.1f}GB)\"\n        )\n        print(\"   - FP16: Enabled\")\n        print(\"   - Batch size: 1 (memory-safe)\")\n        print(\"   - Memory fragmentation: Reduced\")\n        print(\"   - CPU fallback: Available\")\n    else:\n        print(\"⚠️ CUDA not available, using CPU mode\")\n\n\ndef clear_memory():\n    \"\"\"Clear GPU memory\"\"\"\n    if torch.cuda.is_available():\n        torch.cuda.empty_cache()\n    gc.collect()\n\n\ndef get_optimal_settings():\n    \"\"\"Get optimal settings for RTX 3050\"\"\"\n    return {\n        \"num_samples\": 1,  # Safe batch size\n        \"image_resolution\": 256,  # Memory-safe resolution (reduced from 512)\n        \"ddim_steps\": 10,  # Reduced steps for memory efficiency\n        \"use_fp16\": True,  # Enable FP16\n        \"clear_cache\": True,  # Clear cache between runs\n        \"cpu_fallback\": True,  # Enable CPU fallback\n        \"load_on_cpu_first\": True,  # Load model on CPU first\n    }\n\n\ndef load_model_memory_safe(create_model_func, config_path, ckpt_path):\n    \"\"\"\n    Memory-safe model loading for RTX 3050\n\n    This addresses the CUDA OOM issue from test_rtx3050_simple.log\n    \"\"\"\n    print(\"🔧 Loading model with memory-safe strategy...\")\n\n    with memory_manager.memory_efficient_loading():\n        try:\n            # Step 1: Create model on CPU\n            print(\"   - Creating model on CPU...\")\n            model = create_model_func(config_path).cpu()\n\n            # Step 2: Load checkpoint to CPU\n            print(\"   - Loading checkpoint to CPU...\")\n            checkpoint = torch.load(ckpt_path, map_location=\"cpu\")\n\n            # Step 3: Load state dict on CPU\n            print(\"   - Loading state dict on CPU...\")\n            if hasattr(model, \"load_state_dict\"):\n                model.load_state_dict(checkpoint, strict=False)\n\n            # Clean up checkpoint\n            del checkpoint\n            clear_memory()\n\n            # Step 4: Try GPU transfer\n            if torch.cuda.is_available():\n                print(\"   - Attempting GPU transfer...\")\n\n                # Estimate model size\n                param_count = sum(p.numel() for p in model.parameters())\n                model_size_gb = param_count * 4 / 1024**3\n\n                if memory_manager.check_memory_available(model_size_gb):\n                    model = model.cuda().half()  # Move to GPU and convert to FP16\n                    print(\"   ✅ Model loaded on GPU with FP16\")\n                else:\n                    print(\"   ⚠️ Insufficient GPU memory, keeping on CPU\")\n\n            return model\n\n        except Exception as e:\n            print(f\"   ❌ Model loading failed: {e}\")\n            return None\n\n\n# Auto-apply optimizations when imported\nsetup_rtx3050()\n\n# Export key functions\n__all__ = [\n    \"setup_rtx3050\",\n    \"clear_memory\",\n    \"get_optimal_settings\",\n    \"load_model_memory_safe\",\n    \"memory_manager\",\n]\n"}